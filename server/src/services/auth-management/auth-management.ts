// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { NullableId, Params, Id } from "@feathersjs/feathers";
import { symmetricEncrypt } from '../../utils/encryption/symmetric.js';
import { toString } from 'uint8arrays';
import { sendPasswordReset } from './email/index.js';
import { sendPasswordResetText } from './sms/index.js';
import type {Application, HookContext} from '../../declarations.js';
import { authManagementPath } from "./auth-management.shared.js";
import { encodeKeyPair } from 'symbol-ucan';
import {CoreCall} from 'feathers-ucan';

export class AuthManagementService<ServiceParams extends Params = Params> {
    async find(params: Params) {
        return []
    }
    async get(id: Id, params: Params) {}
    async create(data: any, params: Params) {}
    async update(id: NullableId, data: any, params: Params) {}
    async patch(id: NullableId, data: any, params: Params) {}
    async remove(id: NullableId, params: Params) {}
    async setup(app: Application, path: string) {}
    async teardown(app: Application, path: string) {}
}

type ServiceTypes = {
    'auth-management': AuthManagementService
}

const sendHook = async (context:HookContext):Promise<HookContext> => {
    const { login, action, options } = context.data;

    const {key} = context.app.get('mailer') || { key: null };
    const {key:sms_key, id, from } = context.app.get('sms') || { key: null };


    const actions = {
        'verify': async (login, options) => {
            const now = new Date();
            const expired = ((login.verifyExpires || now) < now);
            if (expired) context.result = { login: { ...login, verifyToken: null }, status: 2, message: 'verify token expired' };
            else {
                const encryptionKey = context.app.get('encryption').pin_key;

                const { token } = options;
                const { decrypt } = symmetricEncrypt(encryptionKey);
                const decryptedToken = decrypt(login.verifyToken, true);
                if (token === decryptedToken) {
                    await new CoreCall('logins', context)._patch(login._id, { isVerified: true, verifyToken: null, verifyExpires:  null }, { admin_pass: true, skip_hooks: true })
                    context.result = {
                        login: {
                            _id: login._id,
                            isVerified: true,
                            verifyToken: null,
                            verifyExpires: null
                        },
                        status: 0,
                        message: 'Login Verified!'
                    };
                } else context.result = { login, status: 1, message: 'Verification failed' };
            }
            return context;
        },
        'setVerify': async (login, options) => {
            const encryptionKey = context.app.get('encryption').pin_key;

            const { expireInMinutes = (60 * 24) } = options || {};
            const now = new Date();
            const verifyExpires = new Date(now.setMinutes(now.getMinutes() + expireInMinutes));
            const { encrypt } = symmetricEncrypt(encryptionKey);
            const token = [10, 10, 10, 10].map(a => Math.floor(Math.random() * a)).join('');
            // const token = '1234';
            const verifyToken = toString(encrypt(token), 'hex');
            context.result = { login: { ...login, verifyToken, verifyExpires }, token };
            return context;
        },
        'resetPassword': async (login, options) => {
            const now = new Date();
            const expired = ((login.resetExpires || now) < now);
            if (expired) context.result = { login: { ...login, resetToken: null }, status: 2, message: 'reset token expired' };
            else {
                const encryptionKey = context.app.get('encryption').pin_key;

                const { token } = options;
                const { decrypt } = symmetricEncrypt(encryptionKey);
                const decryptedToken = decrypt(login.resetToken, true);
                if (token === decryptedToken) {
                    context.result =  {
                        login: {
                            _id: login._id,
                            isVerified: true,
                            resetToken: null,
                            resetExpires: null,
                            password: login.pendingPassword,
                            pendingPassword: null
                        },
                        status: 0,
                        message: 'Password Reset Successfully!'
                    };
                } else context.result = { login, status: 1, message: 'Reset password failed' };
            }
            return context;
        },
        'setResetPassword': async (login, options) => {
            const { expireInMinutes = (60 * 24), method = 'email' } = options || {};
            const now = new Date();
            const resetExpires = new Date(now.setMinutes(now.getMinutes() + expireInMinutes));
            const encryptionKey = context.app.get('encryption').pin_key;

            const { encrypt } = symmetricEncrypt(encryptionKey);
            const token = [10, 10, 10, 10].map(a => Math.floor(Math.random() * a)).join('');
            const methods = {
                'email': (pin, lg) => sendPasswordReset(pin, lg, {key}),
                'sms': (pin, lg) => sendPasswordResetText(pin, lg.phone.number.e164, {key: sms_key, id, from })
            };
            await methods[method](token, login);
            const resetToken = toString(encrypt(token), 'hex');
            context.result =  { login: { ...login, resetToken, resetExpires }, token };
            return context;
        }
    };

    return await actions[action](login, options);
}
// A configure function that registers the service and its hooks via `app.configure`
export const authManagement = (app: Application) => {
    // Register our service on the Feathers application
    app.use(authManagementPath, new AuthManagementService(), {
        // A list of all methods this service exposes externally
        methods: ['create', 'get'],
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(authManagementPath).hooks({
        around: {
            all: []
        },
        before: {
            all: [],
            find: [],
            get: [
                (context:HookContext) => {
                    if(context.id === 'rootIssuer'){
                        const {secret} = app.get('authentication') as { [key: string]: any };
                        const rootIssuer = encodeKeyPair({ secretKey:secret }).did();
                        context.result = { rootIssuer }
                    }
                    return context;
                }
            ],
            create: [sendHook],
            patch: [],
            remove: []
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [authManagementPath]: AuthManagementService
    }
}
