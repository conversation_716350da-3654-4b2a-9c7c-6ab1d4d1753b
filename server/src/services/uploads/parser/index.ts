import {HookContext} from '../../../declarations.js';
import xlsx from 'node-xlsx';

type Options = { sheet:string };
export const parseSheet = (buffer:ArrayBufferLike, sheet:string) => {
    let data = xlsx.parse(buffer);
    data = (data || []).map(a => {
        return {...a, data: a.data.filter(b => (b?.length || 0) > 0)}
    })
    return sheet ? data.filter(a => a.name === sheet)[0] : data[0];
}

export const createParse = (context:HookContext):HookContext => {
    const { $limit = 50, $skip = 0 } = (context.params.query || { $limit: 50, $skip: 0 });
    context.params.core = { ...context.params.core, skipJoins: true };
    let data = xlsx.parse(context.params.file.buffer);
    data = (data || []).map(a => {
        return {...a, data: a.data.slice($skip, $limit + $skip).filter(b => (b?.length || 0) > 0)}
    })
    context.result = data;
    return context;
}
