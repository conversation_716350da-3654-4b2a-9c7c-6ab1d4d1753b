// Initializes the `uploads` service on path `/uploads`
// const AWS = require('aws-sdk');
import {Params} from "@feathersjs/feathers";
import {AnyObj} from "../../utils/types";
// import {Application} from "../../delcarations.js";
// import { GeneralError, NotFound } from '@feathersjs/errors';
import {Application} from "../../declarations.js";
import path from 'path';
const getPublicFileUrlPath = 'get-public-file-url';

export class GetPublicFileUrlService<ServiceParams extends Params = Params> {
  app:Application
  // googleConfig: AnyObj
  enums: { STORAGE_TYPES: { [key:string]: string }}
  storage:any
  bucket:string = ''
  uploadsConfig:AnyObj = {}
  constructor(app){
    this.app = app;
    const { enums } = app.get('uploads');
    this.enums = enums;
    this.uploadsConfig = app.get('uploads');
    // this.googleConfig = app.get('google-cloud');
    // if (this.googleConfig && this.googleConfig.projectId) {
    //   this.storage = new Storage({
    //     projectId: this.googleConfig.projectId,
    //     keyFilename: this.googleConfig.keyFilename
    //   });
    //   this.bucket = this.storage.bucket(this.googleConfig.bucket);
    // }
  }
  async find (params:Params&AnyObj) { // this here only to return a Promise
    const { storage, fileId } = params;
    // S3
    let bucket;
    // Google-cloud
    if ((storage === this.enums.STORAGE_TYPES['google-cloud'] || storage === 'google-storage') && bucket) { // google-storage used by old FMC version,
      // const file = bucket.file(fileId);
      // const d = new Date();
      // const url = await file.getSignedUrl({
      //   action: 'read',
      //   expires: new Date(d.getTime(d.getTime() + this.googleConfig.signedUrlExpires || 60*24))
      // });
      // if (!url || !url[0]) {
      //   throw new Error('File not found at google storage' + fileId);
      // }
      // return Promise.resolve(url[0]);
      return '';
    }

    // local-public
    if (storage === this.enums.STORAGE_TYPES['local-public']) {
      const filePath = path.join(this.app.get('public'), 'uploads', params.fileId);

      return Promise.resolve({ public: filePath });
    }
    // local-private
    if (storage === this.enums.STORAGE_TYPES['local-private']) {
      const filePath = path.join(this.uploadsConfig.privateFolder, 'uploads', params.fileId);
      return Promise.resolve({ download: filePath });
    }

    // Other
    if ((!storage || storage === this.enums.STORAGE_TYPES.others) && params.file) {
      console.error('uploads service git file is not support this storage');
      return Promise.resolve(params.file);
    }

    throw new Error('get-public-file-url can\'t handle this request');
  }
}

export const getPublicFileUrl = (app:Application) => {
  app.use(getPublicFileUrlPath, new GetPublicFileUrlService(app));
};

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [getPublicFileUrlPath]: GetPublicFileUrlService
  }
}
