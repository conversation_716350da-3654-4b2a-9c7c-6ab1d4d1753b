// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import type {Application} from '../../declarations.js'
import {MyIpService, getOptions} from './my-ip.class.js'
import {myIpPath, myIpMethods} from './my-ip.shared.js'
import { getClientIp } from '../../utils/ip-utils.js'
import type { HookContext } from '../../declarations.js'

export * from './my-ip.class.js'

// Hook to extract client IP from request (HTTP) or socket (WebSocket) as fallback
// Note: The preferred method is for clients to get their real IP from external services
// and pass it directly to this service
const extractClientIp = (context: HookContext) => {
  // Only extract IP if not already provided
  if (context.params.ip) {
    // console.log('MyIP Service - IP already provided:', context.params.ip);
    return context;
  }

  // console.log('MyIP Service - Attempting IP extraction as fallback');

  // For WebSocket connections, IP is stored in socket.feathers.ip
  if (context.params.provider === 'socketio' && context.params.connection) {
    const socketIp = context.params.connection.feathers?.ip;
    if (socketIp) {
      context.params.ip = socketIp;
      console.log('MyIP Service - Extracted IP from WebSocket:', socketIp);
    }
  }
  // For HTTP requests, extract from request headers
  else if (context.params.provider && context.params.request) {
    const clientIp = getClientIp(context.params.request);
    if (clientIp) {
      context.params.ip = clientIp;
      console.log('MyIP Service - Extracted IP from HTTP:', clientIp);
    }
  }

  if (!context.params.ip) {
    console.log('MyIP Service - No IP could be extracted, service will use fallback');
  }

  return context;
};

// A configure function that registers the service and its hooks via `app.configure`
export const myIp = (app: Application) => {
    // Register our service on the Feathers application
    app.use(myIpPath, new MyIpService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: myIpMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(myIpPath).hooks({
        around: {
            all: []
        },
        before: {
            all: [extractClientIp],
            find: [],
            get: [],
            create: [],
            patch: [],
            remove: []
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [myIpPath]: MyIpService
    }
}
