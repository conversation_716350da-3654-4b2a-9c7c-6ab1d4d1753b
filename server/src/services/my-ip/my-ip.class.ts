// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
//
// MyIP Service Usage:
//
// 1. RECOMMENDED: Pass real IP from external service
//    const realIp = await getUserIP(); // Gets IP from ipify.org
//    const geoData = await myIpService.get(realIp);
//
// 2. Auto-detect (fallback, may not be user's real IP)
//    const geoData = await myIpService.get('1');
//
// 3. Create with IP data
//    const geoData = await myIpService.create({ ip: realIp });
//
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'
import type { Application } from '../../declarations.js'
import {_get} from "../../utils/dash-utils.js";
import geoip from 'geoip-lite';
import logger from '../../utils/logger.js';
import {AnyObj} from "../../utils/types";
import { getClientIp } from '../../utils/ip-utils.js';

type MyIp = any
type MyIpData = any
type MyIpPatch = any
type MyIpQuery = any

export type { MyIp, MyIpData, MyIpPatch, MyIpQuery }

export interface MyIpParams extends MongoDBAdapterParams<MyIpQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class MyIpService<ServiceParams extends Params = MyIpParams> extends MongoDBService<
    MyIp,
    MyIpData,
    MyIpParams,
    MyIpPatch
> {

  private extractIpAddress(id: any, params: ServiceParams&any): string | null {
    // If id is provided and it's not '1', use it as the IP address
    if (id && id !== '1') {
      return id;
    }

    // For id='1', this is a request to auto-detect the IP
    // Try to get IP from various sources in params (fallback only)
    let ip = params.ip ||
             _get(params, 'headers.ip') ||
             _get(params, 'connection.remoteAddress') ||
             _get(params, 'socket.remoteAddress') ||
             _get(params, 'request.ip');

    // If we have a request object, try to extract IP using getClientIp
    if (!ip && params.request) {
      ip = getClientIp(params.request);
    }

    // Clean up IPv6-wrapped IPv4 addresses
    if (ip && ip.includes('::ffff:')) {
      ip = ip.split(':').reverse()[0];
    }

    return ip || null;
  }

  private createGeoResponse(ip: string | null, lookedUpIP: any) {
    const ll = lookedUpIP?.ll ? [lookedUpIP.ll[1], lookedUpIP.ll[0]] : [0, 0];
    return {
      ip: ip || lookedUpIP?.ip || 'unknown',
      ...(lookedUpIP || {}),
      ll,
      lngLat: ll
    };
  }
  async get(id, params:ServiceParams&any) {
    // console.log('MyIP Service - get method called:', { id, paramsIp: params.ip, provider: params.provider });
    let ip = this.extractIpAddress(id, params);

    // console.log('MyIP Service - Extracted IP address:', { ip, id, paramsIp: params.ip });
    logger.info('MyIP Service get:', { ip, id, method: 'get' });

    let lookedUpIP;
    try {
      if (ip) {
        lookedUpIP = geoip.lookup(ip);
        // console.log('MyIP Service - Geoip lookup result:', { ip, success: !!lookedUpIP });
      } else {
        console.log('MyIP Service - No IP address found to lookup');
      }
    } catch (e) {
      // console.error('MyIP Service - geoiplookup fail:', { ip, error: e });
      logger.error('geoiplookup fail', { ip, error: e });
    }

    // Provide fallback when lookup fails
    if (!lookedUpIP) {
      if (!ip || ip === '127.0.0.1' || ip === '::1') {
        // Default fallback for localhost or missing IP
        lookedUpIP = {
          ip: ip || '127.0.0.1',
          range: [],
          country: 'US',
          region: 'North Carolina',
          eu: undefined,
          timezone: 'America/Eastern',
          city: 'Apex',
          ll: [-78.90828, 35.72123],
          metro: 0,
          area: 0,
          error: ip ? 'localhost ip' : 'no ip provided - use external service to get real IP'
        };
      } else {
        // Fallback for when geoip lookup fails for a real IP
        lookedUpIP = {
          ip: ip,
          range: [],
          country: 'Unknown',
          region: 'Unknown',
          eu: undefined,
          timezone: 'UTC',
          city: 'Unknown',
          ll: [0, 0],
          metro: 0,
          area: 0,
          error: 'geoip lookup failed'
        };
      }
    }

    return this.createGeoResponse(ip, lookedUpIP);
  }

  async create(data:AnyObj, params:ServiceParams){
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    // For create, prioritize IP from data, then fall back to auto-detection
    let ip = data.ip || this.extractIpAddress(null, params);

    console.log('MyIP Service - create method called:', { dataIp: data.ip, extractedIp: ip });
    logger.info('MyIP Service create:', { ip, dataIp: data.ip, method: 'create' });

    let lookedUpIP;
    try {
      if (ip) {
        lookedUpIP = geoip.lookup(ip);
        console.log('MyIP Service - Create geoip lookup result:', { ip, success: !!lookedUpIP });
      } else {
        console.log('MyIP Service - Create: No IP address found to lookup');
      }
    } catch (e) {
      console.error('MyIP Service - Create geoiplookup fail:', { ip, error: e });
      logger.error('Create geoiplookup fail', { ip, error: e });
    }

    // Provide fallback when lookup fails
    if (!lookedUpIP) {
      if (!ip || ip === '127.0.0.1' || ip === '::1') {
        // Default fallback for localhost or missing IP
        lookedUpIP = {
          ip: ip || '127.0.0.1',
          range: [],
          country: 'US',
          region: 'North Carolina',
          eu: undefined,
          timezone: 'America/Eastern',
          city: 'Apex',
          ll: [-78.90828, 35.72123],
          metro: 0,
          area: 0,
          error: ip ? 'localhost ip' : 'no ip provided - use external service to get real IP'
        };
      } else {
        // Fallback for when geoip lookup fails for a real IP
        lookedUpIP = {
          ip: ip,
          range: [],
          country: 'Unknown',
          region: 'Unknown',
          eu: undefined,
          timezone: 'UTC',
          city: 'Unknown',
          ll: [0, 0],
          metro: 0,
          area: 0,
          error: 'geoip lookup failed'
        };
      }
    }

    return this.createGeoResponse(ip, lookedUpIP);
  }
}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    multi: true,
    Model: app.get('mongodbClient').then((db) => db.collection('my-ip'))
  }
}
