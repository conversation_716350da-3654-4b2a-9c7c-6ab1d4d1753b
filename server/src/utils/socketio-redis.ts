// Redis adapter configuration for Socket.IO clustering
import { createAdapter } from '@socket.io/redis-adapter';
import { createClient } from 'redis';
import type { Application } from '../declarations.js';

export const configureRedisAdapter = async (app: Application, io: any) => {
    const redisUrl = app.get('sessions').valkey.url;

    if (!redisUrl) {
        console.log('[Socket.IO] No Redis/Valkey URL provided, running in single-server mode');
        return;
    }

    console.log('[Socket.IO] Redis/Valkey URL found, attempting connection...');

    try {
        console.log('[Socket.IO] Configuring Redis/Valkey adapter...');

        // Add timeout to prevent hanging Socket.IO startup
        const connectionTimeout = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Redis connection timeout after 5 seconds')), 5000);
        });

        // Create Redis clients for pub/sub with more resilient settings
        const pubClient = createClient({
            url: redisUrl,
            socket: {
                connectTimeout: 5000,
                lazyConnect: true,
                reconnectStrategy: (retries) => {
                    if (retries > 10) {
                        console.log('[Socket.IO Redis] Max reconnection attempts reached, giving up');
                        return false; // Stop trying to reconnect
                    }
                    return Math.min(retries * 100, 3000); // Exponential backoff up to 3s
                }
            }
        });

        const subClient = pubClient.duplicate();

        // Handle Redis connection events with better error handling
        pubClient.on('error', (err) => {
            console.error('[Socket.IO Redis Pub] Connection error:', err.message);
            // Don't log full stack trace for connection errors to reduce noise
        });

        subClient.on('error', (err) => {
            console.error('[Socket.IO Redis Sub] Connection error:', err.message);
            // Don't log full stack trace for connection errors to reduce noise
        });

        // Handle reconnection events
        pubClient.on('reconnecting', () => {
            console.log('[Socket.IO Redis Pub] Reconnecting...');
        });

        subClient.on('reconnecting', () => {
            console.log('[Socket.IO Redis Sub] Reconnecting...');
        });

        pubClient.on('connect', () => {
            console.log('[Socket.IO Redis Pub] Connected');
        });

        subClient.on('connect', () => {
            console.log('[Socket.IO Redis Sub] Connected');
        });

        // Connect to Redis with timeout protection
        console.log('[Socket.IO] Attempting Redis connection...');
        const connectPromise = Promise.all([
            pubClient.connect(),
            subClient.connect()
        ]);

        await Promise.race([connectPromise, connectionTimeout]);
        console.log('[Socket.IO] Redis clients connected successfully');

        // Configure Socket.IO adapter
        io.adapter(createAdapter(pubClient, subClient, {
            key: 'socket.io',
            requestsTimeout: 5000,
        }));

        console.log('[Socket.IO] Redis adapter configured successfully');

        // Store clients for cleanup
        app.set('redisClients', { pubClient, subClient });

    } catch (error) {
        console.error('[Socket.IO] Failed to configure Redis adapter:', error);
        console.error('[Socket.IO] Error details:', error instanceof Error ? error.message : String(error));
        console.log('[Socket.IO] Falling back to single-server mode');

        // Make sure to clean up any partial connections
        try {
            const clients = app.get('redisClients') as any;
            if (clients && clients.pubClient && clients.subClient) {
                await Promise.allSettled([
                    clients.pubClient.quit(),
                    clients.subClient.quit()
                ]);
            }
        } catch (cleanupError) {
            console.error('[Socket.IO] Error during Redis cleanup:', cleanupError);
        }
    }
};

export const cleanupRedisAdapter = async (app: Application) => {
    const clients = app.get('redisClients') as any;
    if (clients) {
        try {
            await Promise.all([
                clients.pubClient.quit(),
                clients.subClient.quit()
            ]);
            console.log('[Socket.IO] Redis clients disconnected');
        } catch (error) {
            console.error('[Socket.IO] Error disconnecting Redis clients:', error);
        }
    }
};
