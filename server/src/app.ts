// For more information about this file see https://dove.feathersjs.com/guides/cli/application.html
import { feathers } from '@feathersjs/feathers'
import { _get } from './utils/index.js';
import v8 from 'node:v8'
import { monitorEventLoopDelay } from 'node:perf_hooks';

import express, {
    rest,
    json,
    urlencoded,
    cors,
    serveStatic,
    notFound,
    errorHandler
} from '@feathersjs/express'
import configuration from '@feathersjs/configuration'
import socketio from '@feathersjs/socketio'
import { startWatchdog, sampleAllocations } from './utils/watchdog/index.js';
import { requestTimeout, rateLimit, memoryGuard } from './utils/request-safety.js';
import { startProcessMonitoring } from './utils/resource-monitor.js';
// import { configureRedisAdapter } from './utils/socketio-redis.js';
import { startSocketIOMonitoring } from './utils/socketio-monitor.js';

import type { Application } from './declarations.js'
import { configurationValidator } from './configuration.js'
import { logger } from './logger.js'
import { mongodb } from './mongodb.js'
import { services } from './services/index.js'
import { channels } from './channels.js';
import authentication from './authentication/index.js';
import appHooks from './app.hooks.js';

const app: Application = express(feathers())

// // LIVENESS: quick "is the process alive?"
// app.use('/healthz', (_req, res) => res.status(200).send('ok'));
//
// // READINESS: only "ready" when healthy AND dependencies are reachable
// app.use('/readyz', async (_req, res) => {
//     if (readinessBad) return res.status(503).send('degraded');
//
//     // Check critical deps (DB)
//     try {
//         const mongoClient = await app.get('mongodbClient');
//         if (mongoClient) {
//             await mongoClient.admin().ping();
//         } else {
//             return res.status(503).send('db-unavailable');
//         }
//     } catch (e) {
//         console.error('Health check DB ping failed:', e);
//         return res.status(503).send('db-unavailable');
//     }
//
//     return res.status(200).send('ready');
// });



// Load app configuration
app.configure(configuration(configurationValidator))

// Apply safety middleware early
app.use(requestTimeout(45000)) // 45 second timeout
app.use(rateLimit)
app.use(memoryGuard(650)) // Reject requests when heap > 650MB

app.use(cors())
app.use(json({
    verify: (req:any, res, buf) => {
        const rawEndpoints: string[] = ["/banking"];

        if (req.url && rawEndpoints.includes(req.url)) {
            req.rawBody = buf;
        }
    }
}))
app.use(urlencoded({ extended: true }))
// Host the public folder
app.use('/', serveStatic(app.get('public')))

// Configure services and real-time functionality
app.configure(rest())

app.configure(
    socketio({
        cors: {
            origin: app.get('origins'),
        },
        // Connection limits and timeouts
        pingTimeout: 60000,
        pingInterval: 25000,
        upgradeTimeout: 10000,
        maxHttpBufferSize: 1e6, // 1MB
        // Prevent too many connections from same IP
        connectionStateRecovery: {
            maxDisconnectionDuration: 2 * 60 * 1000, // 2 minutes
            skipMiddlewares: true,
        }
    }, async (io) => {
        // Configure Redis adapter for clustering (temporarily disabled for debugging)
        // await configureRedisAdapter(app, io);

        // Connection rate limiting
        const connectionCounts = new Map<string, number>();
        const MAX_CONNECTIONS_PER_IP = 10;

        io.use(function (socket, next) {
            const ip = _get(socket, 'handshake.headers.x-forwarded-for', _get(socket, ['request', 'connection', 'remoteAddress'])) || 'unknown';

            // Rate limit connections per IP
            const currentCount = connectionCounts.get(ip) || 0;
            if (currentCount >= MAX_CONNECTIONS_PER_IP) {
                return next(new Error('Too many connections from this IP'));
            }

            connectionCounts.set(ip, currentCount + 1);

            // Store IP info
            (socket as { [key: string]: any }).feathers.ip = ip;
            (socket as { [key: string]: any }).feathers.headers.ip = ip;

            // Clean up connection count on disconnect
            socket.on('disconnect', () => {
                const count = connectionCounts.get(ip) || 0;
                if (count <= 1) {
                    connectionCounts.delete(ip);
                } else {
                    connectionCounts.set(ip, count - 1);
                }
            });

            next();
        });

        // Start Socket.IO monitoring
        startSocketIOMonitoring(app);
    })
);
app.configure(mongodb)
app.configure(services)


app.configure(authentication)
app.configure(channels)

// Configure a middleware for 404s and the error handler
app.use(notFound())
app.use(errorHandler({ logger }))

// Register hooks that run on all service methods
app.hooks({
        ...{
            around: {
                all: [],
            }
        },
        ...appHooks
    }
);
// Register application setup and teardown hooks here
app.hooks({
    setup: [],
    teardown: [],
});

const hist = monitorEventLoopDelay({ resolution: 20 }); hist.enable();

// Start process monitoring
const stopProcessMonitoring = startProcessMonitoring();

// Start the watchdog AFTER your deps are initialized
startWatchdog({
    // override thresholds via env if you like
    onLeakSuspect: async (info:any, apP:any) => {
        console.warn('[leak suspect]', info)
        try {
            const mu = process.memoryUsage();
            const hs = v8.getHeapStatistics();
            const spaces = v8.getHeapSpaceStatistics();
            const eldP95 = hist.percentile(95) / 1e6; // ms
            await apP.service('errs').create({
                path: 'watchdog',
                method: 'leak-suspect',
                type: 'manual',
                error: {message: 'leak suspect'},
                data: {
                    info,
                    mem: {
                        rssMB: mu.rss / 1048576, heapUsedMB: mu.heapUsed / 1048576,
                        externalMB: mu.external / 1048576
                    },
                    heap: {
                        usedMB: hs.used_heap_size / 1048576,
                        totalMB: hs.total_heap_size / 1048576,
                        limitMB: hs.heap_size_limit / 1048576,
                        spaces: spaces.map(s => ({space: s.space_name, usedMB: s.space_used_size / 1048576}))
                    },
                    eventLoopDelayP95ms: eldP95
                },
                result: {},
                createdAt: new Date()
            })
        } catch (e) {
            console.error('watchdog logging failed on suspect leak', e);
        }
    },
    onHardTrip: async (info:any, apP:any) => {
        console.warn('[hard leak]', info)
        try {
            const profile = await sampleAllocations(10000); // 10s sample window
            // Store a compact summary
            const top = profile.samples
                .slice(0, 1000) // limit, just in case
                .reduce((m, s) => (m[s.nodeId] = (m[s.nodeId]||0) + s.size, m), {});
            console.error('[alloc-top]', Object.entries(top).slice(0,10));
            await apP.service('errs').create({
                path: 'watchdog',
                method: 'hard-trip',
                type: 'manual',
                error: {message: 'hard trip'},
                data: {
                    info,
                    top,
                    profile
                },
                result: {},
                createdAt: new Date()
            })
            // Or save whole `profile` JSON to Mongo/GridFS/S3 if you want
        } catch (e) {
            console.error('sampling-profiler failed on hard trip', e);
        }
    },
}, app);

// // OPTIONAL: admin-only manual heapdump trigger
// app.post('/ops/heapdump', async (req, res) => {
//     if (!process.env.OPS_TOKEN || req.get('x-ops-token') !== process.env.OPS_TOKEN) {
//         return res.status(401).send('unauthorized');
//     }
//     const file = await triggerHeapdump('manual');
//     res.status(200).json({ file });
// });
console.log('[APP] Application setup complete, exporting app');

export {app}
