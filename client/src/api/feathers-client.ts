// src/feathers.ts
import {feathers} from '@feathersjs/feathers';
import socketio from '@feathersjs/socketio-client';
import auth from '@feathersjs/authentication-client';
import io from 'socket.io-client';
// import {batchClient} from 'feathers-batch';
import {iff, discard, paramsForServer} from 'feathers-hooks-common';
import {createPiniaClient} from 'feathers-pinia';
import {createPinia} from 'pinia';
import {LocalStorage, SessionStorage} from 'symbol-auth-client';
import {feathersUrl} from './feathers-connect';

import {useAtcStore} from 'stores/atc-store';

const socket = io(feathersUrl(), {
    transports: ['websocket', 'polling'], // Allow fallback to polling
    timeout: 20000,
    forceNew: true
});

// Add connection debugging
socket.on('connect', () => {
    console.log('[Client] Connected to server:', socket.id);
    console.log('[Client] Server URL:', feathersUrl());
});

socket.on('disconnect', (reason) => {
    console.log('[Client] Disconnected:', reason);
});

socket.on('connect_error', (error) => {
    console.error('[Client] Connection error:', error);
    console.error('[Client] Trying to connect to:', feathersUrl());
});

// Debug authentication events
socket.on('authenticated', (data) => {
    console.log('[Client] Authenticated:', data);
});

socket.on('logout', () => {
    console.log('[Client] Logged out');
});
const paramsWhitelist = ['disableSoftDelete', '$globalAggregate', 'paginate', 'relate_hook', 'core', 'banking', 'runJoin', 'hackId', 'encrypt', 'loginOptions', 'voting', 'crypto', 'addMembers', '_attribute', 'special_change', '_search', '_geo', 'adj', 'caps']

console.log('[Client] Initializing Feathers app...');

const app = feathers();
// app.defaultService = null;

console.log('[Client] Configuring Socket.IO...');
// This variable name becomes the alias for this server.
app
    .configure(socketio(socket as any))
    .configure(auth())
    .hooks({
        before: {
            all: [
                iff(
                    (context: any) => ['create', 'update', 'patch'].includes(context.method),
                    discard('__id', '__isTemp', '__isClone', '_dirty', '_cacheable', '__v_isReadOnly', '__v_isRef')
                ),
                (context: any) => {
                    // console.log('before all', context.path, context.params);
                    const client_ucan = SessionStorage.getItem('client_ucan');
                    //TODO: this is not efficient, only find would pass params in feathers-pinia

                    // const addParams:any = SessionStorage.getItem('add-params');
                    // if(addParams){
                    //     for(const k in addParams){
                    //         context.params[k] = addParams[k];
                    //     }
                    //     SessionStorage.removeItem('add-params');
                    // }
                    context.params.core = {
                        fp: LocalStorage.getItem('fpId'),
                        client_ucan,
                        ucan_aud: LocalStorage.getItem('ucan_aud'),
                        ltail: window.location.href,
                        host: LocalStorage.getItem('host_id'),
                        ref: LocalStorage.getItem('ref_id'),
                    }
                    context.params = paramsForServer(context.params, ...paramsWhitelist);
                    return context;
                },
                // ctx => {
                //     console.log('on my way out', ctx.method, ctx.path);
                // }
                // beforeHook
            ],
            get: [
                (context: any) => {
                    useAtcStore().set_get(context.path, context.id)
                    return context;
                }
            ],
            find: []
        },
        after: {
            all: [],
            create: [],
            find: [],
            get: [
                (context: any) => {
                    useAtcStore().unset_get(context.path, context.id)
                    return context;
                    //     if (context.path === 'banking') {
                    //
                    //         // const {object, data, deleted} = context.result || {};
                    //         // if (object) {
                    //         //     if (deleted) useStripeStore().parseStripeResults(`${object}__remove`, context.result);
                    //         //     else useStripeStore().parseStripeResults(object, context.result)
                    //         // } else if (data && Array.isArray(data) && data[0].object) {
                    //         //     useStripeStore().parseStripeResults(`${data[0].object}__list`, context.result);
                    //         // }
                    //     }
                    //     return context;
                }
            ]
        },
        error: {
            all: []
        }
    })
// .configure(batchClient({
//     batchService: 'batch',
//     exclude: ['authentication'],
// }));

console.log('[Client] Feathers app configured');

export const feathersClient = app;

export const createStore = ((/* { ssrContext } */) => {
    const p = createPinia();
    // You can add Pinia plugins here
    // pinia.use(SomePiniaPlugin)

    return p;
});
console.log('[Client] Creating Pinia store...');
export const pinia = createStore();

console.log('[Client] Creating Pinia client...');
// export const api = createPiniaClient<Application<any, any>>(feathersClient, {
export const api = createPiniaClient(feathersClient, {
    pinia,
    idField: '_id',
    ssr: false,
    paramsForServer: paramsWhitelist,
    skipGetIfExists: true,
    setupInstance(data: any) {
        return data;
    },
    services: {
        logins: {},
        myIp: {
            idField: 'ip'
        },
        auth: {},
        banking: {},
        uploads: {},
        fingerprints: {},
        servicesExpose: {
            idField: 'service'
        },
        authManagement: {},
        orgs: {},
        ppls: {},
        projects: {},
        refs: {},
        reqs: {},
        reviews: {},
        tomtomGeocode: {},
        tomtomReverseGeocode: {}
    }
});

console.log('[Client] Feathers client setup complete');

console.log('[Client] Feathers client setup complete');
