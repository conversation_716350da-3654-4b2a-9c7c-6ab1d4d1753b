// app global css in SCSS form
input:required + div.q-field__label::after,
.required .q-field__label::after,
.required .q-checkbox__label::after,
.required .q-toggle__label::after,
.required .q-radio__label::after {
  content: " *";
  color: red;
}

@font-face {
  font-family: VisbyCF;
  src: url('./fonts/VisbyCF-Regular.woff') format('woff');
  font-weight: 400;
}

@font-face {
  font-family: VisbyCF;
  src: url('./fonts/VisbyCF-RegularOblique.woff') format('woff');
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: VisbyCF;
  src: url('./fonts/VisbyCF-Light.woff') format('woff');
  font-weight: 300;
}

@font-face {
  font-family: VisbyCF;
  src: url('./fonts/VisbyCF-LightOblique.woff') format('woff');
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: VisbyCF;
  src: url('./fonts/VisbyCF-Thin.woff') format('woff');
  font-weight: 200;
}

@font-face {
  font-family: VisbyCF;
  src: url('./fonts/VisbyCF-Bold.woff') format('woff');
  font-weight: 700;
}

@font-face {
  font-family: VisbyCF;
  src: url('./fonts/VisbyCF-Medium.woff') format('woff');
  font-weight: 500;
}

@font-face {
  font-family: VisbyCF;
  src: url('./fonts/VisbyCF-ExtraBold.woff') format('woff');
  font-weight: 800;
}


@font-face {
  font-family: VisbyRound;
  src: url('./fonts/VisbyRoundCF-Regular.woff') format('woff');
  font-weight: 400;
}

@font-face {
  font-family: VisbyRound;
  src: url('./fonts/VisbyRoundCF-RegularOblique.woff') format('woff');
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: VisbyRound;
  src: url('./fonts/VisbyRoundCF-Light.woff') format('woff');
  font-weight: 300;
}

@font-face {
  font-family: VisbyRound;
  src: url('./fonts/VisbyRoundCF-LightOblique.woff') format('woff');
  font-weight: 300;
  font-style: italic;
}

@font-face {
  font-family: VisbyRound;
  src: url('./fonts/VisbyRoundCF-ExtraLight.woff') format('woff');
  font-weight: 200;
}

@font-face {
  font-family: VisbyRound;
  src: url('./fonts/VisbyRoundCF-Medium.woff') format('woff');
  font-weight: 500;
}

@font-face {
  font-family: VisbyRound;
  src: url('./fonts/VisbyRoundCF-Bold.woff') format('woff');
  font-weight: 700;
}

@font-face {
  font-family: VisbyRound;
  src: url('./fonts/VisbyRoundCF-Heavy.woff') format('woff');
  font-weight: 800;
}

//GLOBAL OVERRIDES
.md-editor-preview {
  word-break: normal !important;
}

@font-face {
  font-family: 'password';
  font-style: normal;
  font-weight: 400;
  src: url(https://jsbin-user-assets.s3.amazonaws.com/rafaelcastrocouto/password.ttf);
}

._hide {
  font-family: 'password' !important;
}

.pss input {
  font-family: 'password' !important;
}

//END GLOBAL OVERRIDES


.main-font {
  font-family: 'VisbyCF', serif;
}

body {
  font-family: 'VisbyCF', serif;
}

.alt-font {
  font-family: 'VisbyRound', sans-serif;
}

.num-font {
  font-family: 'VisbyRound', sans-serif;
}

@keyframes roam_l {
  0% {
    transform: translate(-5%, 0);
  }
  10% {
    transform: translate(-25%, 10%)
  }
  20% {
    transform: translate(-23%, 30%)
  }
  30% {
    transform: translate(-13%, 20%)
  }
  40% {
    transform: translate(0%, 0%)
  }
  50% {
    transform: translate(10%, -10%)
  }
  60% {
    transform: translate(18%, -30%)
  }
  70% {
    transform: translate(28%, -40%)
  }
  80% {
    transform: translate(22%, -28%)
  }
  90% {
    transform: translate(5%, -10%)
  }
  100% {
    transform: translate(-5%, 0);
  }
}

@keyframes roam {
  0% {
    transform: translate(5%, 0);
  }
  10% {
    transform: translate(8%, -5%)
  }
  20% {
    transform: translate(10%, -10%)
  }
  30% {
    transform: translate(5%, -10%)
  }
  40% {
    transform: translate(0%, 0%)
  }
  50% {
    transform: translate(-5%, 3%)
  }
  60% {
    transform: translate(-8%, 10%)
  }
  70% {
    transform: translate(-12%, 13%)
  }
  80% {
    transform: translate(-12%, 11%)
  }
  90% {
    transform: translate(-5%, 8%)
  }
  100% {
    transform: translate(5%, 0);
  }
}

@keyframes roam_m {
  0% {
    transform: translate(5%, 0);
  }
  10% {
    transform: translate(15%, 8%)
  }
  20% {
    transform: translate(10%, 0%)
  }
  30% {
    transform: translate(5%, -10%)
  }
  40% {
    transform: translate(0%, 0%)
  }
  50% {
    transform: translate(-10%, 6%)
  }
  60% {
    transform: translate(-15%, 12%)
  }
  70% {
    transform: translate(-18%, 16%)
  }
  80% {
    transform: translate(-12%, 11%)
  }
  90% {
    transform: translate(-5%, 8%)
  }
  100% {
    transform: translate(5%, 0);
  }
}

._top {
  border-radius: 0 0 20px 20px;
  box-shadow: 0 8px 18px -18px #999;
}

._col {
  display: flex;
  flex-direction: column;
}

._i_i {
  transform: translate(0, -.1rem);
}
._i_2 {
  transform: translate(0, -2px);
}

._l_btn {
  background: linear-gradient(98deg, var(--q-p4), var(--q-p6));
  color: white !important;
}

._p_btn {
  background: linear-gradient(98deg, var(--q-p9), var(--q-p6));
  color: white !important;
}
._pl_btn {
  background: linear-gradient(8deg, var(--q-p4), var(--q-primary));
  color: white !important;
}

._s_btn {
  background: linear-gradient(98deg, var(--q-s9), var(--q-s6));
  color: white !important;
}
._sl_btn {
  background: linear-gradient(98deg, var(--q-s4), var(--q-s6));
  color: white !important;
}

._a_btn {
  background: linear-gradient(98deg, var(--q-a7), var(--q-accent));
  color: white !important;
}

._bg_ow {
  background-color: #fbffff
}

._bg_airy {
  background: linear-gradient(12deg, var(--q-a0), white);
}

._bg_g {
  background-color: #f8f8f8;
}

._bub {
  border-radius: 7px;
  background: linear-gradient(45deg, white, #f0f0f0);
  box-shadow: 0 2px 15px -7px #999;
}
._common_inp {
  border: none;
  font-size: .8rem;
  font-weight: 500;
  width: 100%;
  background: transparent;
}

._inp {
  padding: 0 10px;
  margin: 0 2px;
  border-radius: 8px;
  box-shadow: 0 2px 6px -2px var(--ir-mid);
  background: var(--ir-bg);
}

._flat {
  box-shadow: none !important;
}

._flip_off {
  transform: none;
  transition: transform .3s;
}

._flip {
  transform: rotateX(180deg);
}

._flop {
  transform: rotateY(180deg);
}

._finp {
  background-color: #f6f6f6;
  border-radius: 12px;
  padding: 0 5px;
}

._f_g {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto;
  width: 100%;
  //display: flex;
  //flex-direction: column;
  //align-items: flex-start;
}

._f_l {
  width: 100%;
  color: #666;
  text-align: left;
  font-size: 1rem;
  font-weight: 600;
  padding: 10px;
  margin: 15px 0 0 0;
  flex-grow: 0;
  flex-shrink: 1;
}

._f_chip {
  border-radius: 5px;
  background: linear-gradient(184deg, transparent, var(--q-p0));
  box-shadow: 0 2px 2px rgba(0, 0, 0, .1);
  //background: #eeeeee;
  color: var(--q-p9) !important;
  //color: var(--q-primary) !important;
}

._s_chip {
  border-radius: 5px;
  background: linear-gradient(184deg, transparent, var(--q-s0));
  box-shadow: 0 2px 2px rgba(0, 0, 0, .1);
  //background: #eeeeee;
  color: var(--q-s9) !important;
  //color: var(--q-primary) !important;
}

._a_chip {
  border-radius: 5px;
  background: linear-gradient(184deg, transparent, var(--q-a0));
  box-shadow: 0 2px 2px rgba(0, 0, 0, .1);
  //background: #eeeeee;
  color: var(--q-a9) !important;
  //color: var(--q-primary) !important;
}

._frm {
  border-radius: 8px;
  //box-shadow: -2px -2px 12px var(--ir-light);
  transition: all .2s;
  position: relative;
  display: grid;
  grid-template-columns: min(30%, 150px) 1fr;
  align-items: center;

  ._bod {
    padding: 6px;
    border-radius: 0 8px 8px 0;
  }

  ._lbl {
    font-size: .8rem;
    font-weight: 600;
    color: var(--ir-mid);
    font-family: var(--alt-font);
    border-radius: 8px 0 0 8px;
    padding: 12px 6px;
    border-right: solid 2px var(--ir-light);
    height: 100%;
    align-content: start;


    > span {
      font-weight: 500;
      font-size: .75rem;
    }
  }

}

@media screen and (max-width: 600px) {
  ._frm{
    grid-template-columns: 100%;

    ._lbl {
      padding: 12px 6px 0 6px;
    }
  }
}

._form_label {
  color: var(--ir-deep);
  font-size: .8rem;
  font-weight: 600;
  height: 100%;
  max-width: 140px;
  border-right: solid 1px rgba(0, 0, 0, .3);
  padding: 12px 20px 10px 20px;
  display: inline-block;
  align-content: start;
}

._form_grid {
  display: grid;
  grid-template-columns: auto 1fr;
  grid-template-rows: repeat(auto-fill, auto);
  align-items: start;

}

._form_grid_m {
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: auto;
  align-items: start;
}

._form_grid_dark {
  ._form_label {
    color: #dedede;
    border-right: solid 1px rgba(255,255,255, .3);
  }
}


._f_g_r {
  ._form_label {
    text-align: right;
    width: 140px;
  }
}

@media screen and (max-width: 1023px) {
  ._form_grid {
    grid-template-columns: 100%;
    grid-template-rows: auto;
  }
  ._form_label {
    border-right: none;
    padding: 15px 10px 5px 10px;
    max-width: 100%;
    width: 100%;
  }
  ._form_grid_dark {
    ._form_label {
      border-right: none;
      padding: 15px 10px 5px 10px;
      max-width: 100%;
      width: 100%;
    }
  }
  ._f_g_r {
    ._form_label {
      text-align: left !important;
      width: 100%;
    }
  }
}

//GLOBAL ITEMS

.tw-one {
  font-weight: 100;
}

.tw-two {
  font-weight: 200;
}

.tw-three {
  font-weight: 300;
}

.tw-four {
  font-weight: 400;
}

.tw-five {
  font-weight: 500;
}

.tw-six {
  font-weight: 600;
}

.tw-seven {
  font-weight: 700;
}

.tw-eight {
  font-weight: 800;
}

.tw-nine {
  font-weight: 900;
}

.h10 {
  height: 10px;
}
.h15 {
  height: 15px;
}

.h20 {
  height: 20px;
}
.h25 {
  height: 25px;
}


.h30 {
  height: 30px;
}

.h40 {
  height: 40px;
}

.h50 {
  height: 50px;
}

.h60 {
  height: 60px;
}

.h70 {
  height: 70px;
}

.h80 {
  height: 80px;
}

.h90 {
  height: 90px;
}

.h100 {
  height: 100px;
}

.h120 {
  height: 120px;
}

.h130 {
  height: 130px;
}

.h150 {
  height: 150px;
}

.h200 {
  height: 200px;
}

.h250 {
  height: 250px;
}

.h280 {
  height: 280px;
}

.h300 {
  height: 300px;
}

.h350 {
  height: 350px;
}

.h400 {
  height: 400px;
}

.h450 {
  height: 450px;
}

.h500 {
  height: 500px;
}

.h550 {
  height: 550px;
}

.h600 {
  height: 600px;
}

.h650 {
  height: 650px;
}

.h700 {
  height: 700px;
}

.h750 {
  height: 750px;
}

.h800 {
  height: 800px;
}

.h850 {
  height: 850px;
}

.h900 {
  height: 950px;
}

.h1000 {
  height: 1000px;
}

.h1100 {
  height: 1100px;
}

.h1200 {
  height: 1200px;
}

.w5 {
  width: 5px;
}

.w10 {
  width: 10px;
}
.w15 {
  width: 15px;
}

.w20 {
  width: 20px;
}
.w25 {
  width: 25px;
}

.w30 {
  width: 30px;
}

.w40 {
  width: 40px;
}

.w50 {
  width: 50px;
}

.w60 {
  width: 60px;
}

.w70 {
  width: 70px;
}

.w80 {
  width: 80px;
}

.w90 {
  width: 90px;
}

.w100 {
  width: 100px;
}

.w120 {
  width: 120px;
}

.w130 {
  width: 130px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.w250 {
  width: 250px;
}

.w300 {
  width: 300px;
}

.w350 {
  width: 350px;
}

.w400 {
  width: 400px;
}

.w450 {
  width: 450px;
}

.w500 {
  width: 500px;
}

.w550 {
  width: 550px;
}

.w600 {
  width: 600px;
}

.w650 {
  width: 650px;
}

.w700 {
  width: 700px;
}

.w750 {
  width: 750px;
}

.w800 {
  width: 800px;
}

.w850 {
  width: 850px;
}

.w900 {
  width: 950px;
}

.w1000 {
  width: 1000px;
}

.w1100 {
  width: 1100px;
}

.w1200 {
  width: 1200px;
}

.mw200 {
  max-width: 200px;
}

.mw300 {
  max-width: 300px;
}

.mw400 {
  max-width: 400px;
}

.mw500 {
  max-width: 500px;
}

.mw600 {
  max-width: 600px;
}
.mw700 {
  max-width: 700px;
}
.mw800 {
  max-width: 800px;
}
.mw900 {
  max-width: 900px;
}
.mw1000 {
  max-width: 1000px;
}

.mw100 {
  max-width: 100%;
}

.mw100p {
  max-width: 100px;
}

.mw90 {
  max-width: 90%;
}

.mw80 {
  max-width: 80%;
}

.mw60 {
  max-width: 60%;
}

.mw50 {
  max-width: 50%;
}
.mw47 {
  max-width: 47%;
}

.mw30 {
  max-width: 30%;
}

.mw40 {
  max-width: 40%;
}

.mxw50 {
  max-width: 50px;
}

.mxw80 {
  max-width: 80px;
}

.mxw100 {
  max-width: 100px;
}

.mxw200 {
  max-width: 200px;
}

.mxw300 {
  max-width: 300px;
}

.mxw400 {
  max-width: 400px;
}

.mnw100 {
  min-width: 100px;
}

.mnw150 {
  min-width: 150px;
}

.mnw200 {
  min-width: 200px;
}
.mnw600 {
  min-width: 600px;
}

.mh100 {
  max-height: 100%;
}

.mh90 {
  max-height: 90%;
}

.mh80 {
  max-height: 80%;
}

.mnh100 {
  min-height: 100vh
}
.mnh500 {
  min-height: 500px;
}
.mnh400 {
  min-height: 400px;
}
.mnh90 {
  min-height: 90vh
}

.mnh80 {
  min-height: 80vh
}

.mnh60 {
  min-height: 60vh
}


.mnh50 {
  min-height: 50vh
}

.mnh200 {
  min-height: 200px;
}

._pt12 {
  padding-top: 12px;
}
._pb12 {
  padding-bottom: 12px;
}
._py12 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.pa1 {
  padding: 1vw
}

.pa2 {
  padding: 2vw
}

.pa3 {
  padding: 3vw
}

.pd1 {
  padding-top: 1vh;
  padding-bottom: 1vh;
}

.pd2 {
  padding-top: 2vh;
  padding-bottom: 2vh;
}


.pd3 {
  padding-top: 3vh;
  padding-bottom: 3vh;
}

.pd4 {
  padding-top: 4vh;
  padding-bottom: 4vh;
}

.pd5 {
  padding-top: 5vh;
  padding-bottom: 5vh;
}

.pd6 {
  padding-top: 6vh;
  padding-bottom: 6vh;
}

.pd7 {
  padding-top: 7vh;
  padding-bottom: 7vh;
}

.pd8 {
  padding-top: 8vh;
  padding-bottom: 8vh;
}

.pd10 {
  padding-top: 10vh;
  padding-bottom: 10vh;
}

.pd12 {
  padding-top: 12vh;
  padding-bottom: 12vh;
}

.pd15 {
  padding-top: max(80px, 15vh);
  padding-bottom:  max(80px, 15vh);
}

.pw1 {
  padding-right: 1vw;
  padding-left: 1vw;
}

.pw2 {
  padding-right: max(2vw, 20px);
  padding-left: max(2vw, 20px);
}

.pw3 {
  padding-right: 3vw;
  padding-left: 3vw;
}

.pw4 {
  padding-right: 4vw;
  padding-left: 4vw;
}

.pw5 {
  padding-right: 5vw;
  padding-left: 5vw;
}

._hov {
  transition: transform .3s;

  &:hover {
    transform: translate(0, -3px);
  }
}

._oh {
  overflow: hidden;
}
._oxh {
  overflow-x: hidden;
}
._oxs {
  overflow-x: scroll;
}

//Global card sytle
._c {
  border-radius: 12px !important;
  box-shadow: 0 25px 50px -22px rgb(0, 0, 0, 25%) !important;
}

._c1 {
  border-radius: 12px !important;
  box-shadow: 0 0 20px -12px rgb(0, 0, 0, 55%) !important;
}

._bx {
  background: white;
  border-radius: 8px;
  box-shadow: 0 0 4px rgba(99, 99, 99, .3);
}

._oh {
  overflow: hidden;
}

._p0 {
  padding: 0 !important;
}

._panel {
  padding: 0 !important;
  background: transparent;
}

._fg0 {
  flex-grow: 0
}

._fg1 {
  flex-grow: 1
}

._fg2 {
  flex-grow: 2
}

._fg3 {
  flex-grow: 3
}

._fg4 {
  flex-grow: 4
}

._fg5 {
  flex-grow: 5
}

._fg6 {
  flex-grow: 6
}

._fg7 {
  flex-grow: 7
}

._fg8 {
  flex-grow: 8
}

._fg9 {
  flex-grow: 9
}

._fg10 {
  flex-grow: 10
}

$dark-02: rgba(0, 0, 0, .2);
$dark-03: rgba(0, 0, 0, .3);
$dark-04: rgba(0, 0, 0, .4);
$dark-05: rgba(0, 0, 0, .5);

.bg-nice {
  background: var(--q-nice);
}

.preline {
  white-space: pre-line;
  word-break: break-word;
}

.full-height {
  height: 100%;
}

._cent {
  width: 1500px;
  max-width: 100%;
}

._sent {
  width: 1000px;
  max-width: 100%;
}

._xsent {
  width: 800px;
  max-width: 100%;
}

.br5 {
  border-radius: 5px;
}

.br6 {
  border-radius: 6px;
}

.br8 {
  border-radius: 8px;
}

.br10 {
  border-radius: 10px;
}

.br15 {
  border-radius: 15px;
}

.br20 {
  border-radius: 20px;
}

.br30 {
  border-radius: 30px;
}

.br50 {
  border-radius: 50%
}

.bs2-5 {
  box-shadow: 0 2px 5px rgba(0, 0, 0, .1)
}

.bs2-8 {
  box-shadow: 0 2px 8px rgba(0, 0, 0, .2)
}

.bs3-12 {
  box-shadow: 0 2px 12px rgba(0, 0, 0, .2)
}

@media screen and (max-width: 1500px) {
  ._cent {
    width: 92vw;
    max-width: 100%;
  }
}


@media screen and (max-width: 1400px) {
  ._cent {
    width: 95vw;
    max-width: 100%;
  }
  ._sent {
    width: 70vw;
    max-width: 100%;
  }
}

._flat {
  box-shadow: none !important;
}

@media screen and (max-width: 1000px) {
  ._cent {
    width: 100%;
  }
  ._sent {
    width: 100%;
  }

  .pd3 {
    padding-top: 3vw;
    padding-bottom: 3vw;
  }
  .pd5 {
    padding-top: 5vw;
    padding-bottom: 5vw;
  }
  .pd6 {
    padding-top: 6vw;
    padding-bottom: 6vw;
  }
  .pd7 {
    padding-top: 7vw;
    padding-bottom: 7vw;
  }
  .pd8 {
    padding-top: 8vw;
    padding-bottom: 8vw;
  }
  .pd10 {
    padding-top: 10vw;
    padding-bottom: 10vw;
  }
  .pd12 {
    padding-top: 12vw;
    padding-bottom: 12vw;
  }
  .pd15 {
    padding-top: 15vw;
    padding-bottom: 15vw;
  }
}


._l0-5 {
  line-height: .5
}

._l0-7 {
  line-height: .7
}

._l0-8 {
  line-height: .8
}

._l0-9 {
  line-height: .9
}

._l1 {
  line-height: 1
}

._l1-1 {
  line-height: 1.1
}

._l1-2 {
  line-height: 1.2
}

._l1-3 {
  line-height: 1.3
}

._l1-4 {
  line-height: 1.4
}

._l1-5 {
  line-height: 1.5
}

._tool {
  height: 100%;
  width: 100%;
  border-radius: 8px;
  transform: translate(0, -20%);
  padding: 2px;
  position: relative;
  display: grid;
  grid-template-columns: auto 1fr;
  grid-template-rows: 100%;
  box-shadow: 1px 3px 7px rgba(0, 0, 0, .4);
}

.relative-position {
  position: relative
}

.text-shade-4 {
  color: rgba(0, 0, 0, .4);
}

.bg-shade-2 {
  background: rgba(0, 0, 0, .2);
}

.bg-shade-4 {
  background: rgba(0, 0, 0, .4);
}

.bg-shade-5 {
  background: rgba(0, 0, 0, .5);
}

.bg-shade-6 {
  background: rgba(0, 0, 0, .6);
}

.bg-shade-7 {
  background: rgba(0, 0, 0, .7);
}

.bg-shade-8 {
  background: rgba(0, 0, 0, .8);
}

.bg-light-4 {
  background: rgba(255, 255, 255, .4);
}

.text-light-4 {
  color: rgba(255, 255, 255, .4);
}

.bg-light-5 {
  background: rgba(255, 255, 255, .5);
}

.bg-light-6 {
  background: rgba(255, 255, 255, .6);
}

.bg-light-8 {
  background: rgba(255, 255, 255, .8);
}

.bg-light-8-5 {
  background: rgba(255, 255, 255, .85);
}

.bg-light-9 {
  background: rgba(255, 255, 255, .9);
}

._invisible {
  display: none;
}

._fw {
  width: 100%;
}

._fh {
  height: 100%;
}

._fa {
  width: 100%;
  height: 100%;
}

._one_liner {
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
}

.fill_size {
  height: 100%;
  width: 100%;
}

._o1 {
  opacity: .1;
}
._o2 {
  opacity: .2;
}
._o3 {
  opacity: .3;
}
._o4 {
  opacity: .4;
}
._o5 {
  opacity: .5;
}
.t-r {
  position: absolute;
  top: 3px;
  right: 3px;
  z-index: 2
}

.t-r-a {
  position: absolute;
  top: 0px;
  right: 0px;
  z-index: 2
}


.t-r-f {
  position: fixed;
  top: 3px;
  right: 3px;
  z-index: 2
}

.t-l-f {
  position: fixed;
  top: 3px;
  left: 3px;
  z-index: 2
}

.t-l-a {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2
}

.b-r-a {
  position: absolute;
  bottom: 0px;
  right: 0px;
  z-index: 2
}

.b-r-f {
  position: fixed;
  bottom: 0px;
  right: 0px;
  z-index: 200
}

.b-r {
  position: absolute;
  bottom: 3px;
  right: 3px;
  z-index: 2
}

.b-l-a {
  position: absolute;
  bottom: 0px;
  left: 0px;
  z-index: 2
}

.b-l-f {
  position: fixed;
  bottom: 3px;
  left: 3px;
  z-index: 200;
}

.b-l {
  position: absolute;
  bottom: 3px;
  left: 3px;
  z-index: 2
}

.t-l {
  position: absolute;
  top: 3px;
  left: 3px;
  z-index: 2
}

.overlay_8 {
  position: fixed;
  top: 0;
  left: 0;
  height: 102vh;
  width: 102vw;
  background: rgba(0, 0, 0, .8);
  z-index: 9999;
}

.btn_top_right {
  position: absolute;
  top: 5px;
  right: 5px
}

.fill-height {
  height: 100%
}

.fill-width {
  width: 100%
}


.pointer {
  cursor: pointer
}

.--y-scroll {
  height: 100vh;
  overflow-y: scroll;
}

.text-p7 {
  color: $p7;
}

.text-s7 {
  color: $s7;
}

.text-s9 {
  color: $s9;
}

.text-primary {
  color: $primary
}

.text-0-7 {
  font-size: .7em
}

.text-0-8 {
  font-size: .8em
}

.text-0-9 {
  font-size: .9em
}

.text-1 {
  font-size: 1em
}

.text-1-1 {
  font-size: 1.1em
}

.text-1-2 {
  font-size: 1.2em
}

.text-1-3 {
  font-size: 1.3em
}

.text-1-4 {
  font-size: 1.4em
}

.text-1-5 {
  font-size: 1.5em
}

.text-1-6 {
  font-size: 1.6em
}

.text-1-7 {
  font-size: 1.7em
}

.bg-background {
  background: $background
}

.bg-primary-grad {
  background: linear-gradient(196deg, $primary, #fafafa)
}

.bg-dark-grad {
  background: linear-gradient(196deg, $primary, $dark)
}

.bg-rdark-grad {
  background: linear-gradient(-16deg, $primary -15%, $dark)
}

.bg-primary-accent {
  background: linear-gradient(196deg, $primary, $accent)
}

.bg-primary-secondary {
  background: linear-gradient(196deg, $primary, $secondary)
}

.bg-accent-secondary {
  background: linear-gradient(196deg, $accent, $secondary)
}

.bg-accent-primary {
  background: linear-gradient(196deg, $accent, $primary)
}

.text-secondary {
  color: $secondary
}

.bg-secondary {
  background: $secondary
}

.bg-nice {
  background: $nice;
}

.text-nice {
  color: $nice;
}

.text-accent {
  color: $accent
}

.bg-accent {
  background: $accent
}

.bg-accent-grad {
  background: linear-gradient(196deg, $accent 20%, #101010)
}

.bg-primary-grad-trans {
  background: linear-gradient(196deg, $primary -20%, transparent 20%)
}

.bg-accent-grad-trans {
  background: linear-gradient(196deg, $accent -20%, transparent 20%)
}

.bg-secondary-grad-trans {
  background: linear-gradient(196deg, $secondary -20%, transparent 20%)
}

.bg-accent-grad-rev {
  background: linear-gradient(16deg, $accent, #101010)
}

.text-dark2 {
  color: $dark-02
}

.text-dark3 {
  color: $dark-03
}

.text-dark4 {
  color: $dark-04
}

.text-dark5 {
  color: $dark-05
}

.bg-dark-02 {
  background: $dark-02
}

.bg-dark-03 {
  background: $dark-03
}

.bg-dark-04 {
  background: $dark-04
}

.bg-dark-05 {
  background: $dark-05
}

.text-dark-05 {
  color: $dark-05
}


.font-08 {
  font-size: 8px;
}

.font-09 {
  font-size: 9px;
}

.font-10 {
  font-size: 10px;
}

.font-11 {
  font-size: 11px;
}

.font-12 {
  font-size: 12px;
}

.font-13 {
  font-size: 13px;
}

.font-14 {
  font-size: 14px;
}

.font-15 {
  font-size: 15px;
}

.font-16 {
  font-size: 16px;
}

.font-17 {
  font-size: 17px;
}

.font-18 {
  font-size: 18px;
}

.font-19 {
  font-size: 19px;
}

.font-20 {
  font-size: 20px;
}

.font-21 {
  font-size: 21px;
}

.font-22 {
  font-size: 22px;
}

.font-23 {
  font-size: 23px;
}

.font-24 {
  font-size: 24px;
}

.font-25 {
  font-size: 25px;
}

.font-30 {
  font-size: 30px;
}

.font-35 {
  font-size: 35px;
}

.font-40 {
  font-size: 40px;
}

.font-50 {
  font-size: 50px;
}

.font-1-2r {
  font-size: .5rem;
}

.font-5-8r {
  font-size: .625rem;
}

.font-1-3r {
  font-size: .33rem;
}

.font-2-3r {
  font-size: .66rem;
}

.font-3-4r {
  font-size: .75rem;
}

.font-7-8r {
  font-size: .88rem;
}

.font-1r {
  font-size: 1rem;
}

.font-1-1-8r {
  font-size: 1.12rem;
}

.font-1-1-4r {
  font-size: 1.25rem;
}

.font-1-3-8r {
  font-size: 1.37rem;
}

.font-1-1-2r {
  font-size: 1.5rem;
}

.font-1-2-3r {
  font-size: 1.66rem;
}

.font-1-3-4r {
  font-size: 1.75rem;
}

.font-2r {
  font-size: 2rem;
}

.font-2-1-3r {
  font-size: 2.33rem;
}

.font-2-1-2r {
  font-size: 2.5rem;
}

.font-3r {
  font-size: 3rem;
}

.font-3-1-2r {
  font-size: 3.5rem;
}

.font-4r {
  font-size: 4rem;
}

.font-4-1-2r {
  font-size: 4.5rem;
}

.font-5r {
  font-size: 5rem;
}

.font-5-1-2r {
  font-size: 5.5rem;
}

.font-6r {
  font-size: 6rem;
}

.font-7r {
  font-size: 7rem;
}


.z0 {
  z-index: 0
}

.z1 {
  z-index: 1 !important;
}

.z2 {
  z-index: 2 !important;
}

.z3 {
  z-index: 3 !important;
}

.z4 {
  z-index: 4 !important;
}

.z5 {
  z-index: 5 !important;
}

.z6 {
  z-index: 6 !important;
}

.z10 {
  z-index: 10 !important;
}

.z20 {
  z-index: 20
}

.text-huge {
  font-size: 6vw
}

.text-big {
  font-size: 5vw
}

.text-xxxl {
  font-size: 4vw
}

.text-xxl {
  font-size: 3vw
}

.text-xl {
  font-size: 2.3vw
}

.text-lg {
  font-size: 1.7vw
}

.text-md {
  font-size: 1.5vw
}

.text-sm {
  font-size: 1.25vw
}

.text-xs {
  font-size: max(14px, 1vw);
}

.text-xxs {
  font-size: max(12px, .75vw);
}

.text-xxxs {
  font-size: max(10px, .65vw);
}

@media screen and (min-width: 1700px) {
  .text-huge,
  .text-mb-huge {
    font-size: 5.5vw !important;
  }
  .text-big, .text-mb-big {
    font-size: 4.5vw !important;
  }
}

@media screen and (min-width: 2200px) {
  .text-huge, .text-mb-huge {
    font-size: 4vw !important;
  }
  .text-big, .text-mb-big {
    font-size: 3.5vw !important;
  }
}

@media screen and (max-width: 1330px) {
  .text-huge, .text-mb-huge {
    font-size: 7vw !important;
  }
  .text-big, .text-mb-big {
    font-size: 5.5vw !important;
  }
  .text-xxxl,
  .text-mb-xxxl {
    font-size: 4.5vw !important;
  }
  .text-xxl,
  .text-mb-xxl {
    font-size: 3.4vw !important
  }

  .text-xl,
  .text-mb-xl {
    font-size: 2.65vw !important
  }
  .text-lg,
  .text-mb-lg {
    font-size: 2.2vw !important
  }
  .text-md,
  .text-mb-md {
    font-size: 1.75vw !important
  }
  .text-sm,
  .text-mb-sm {
    font-size: 1.6vw !important
  }
  .text-xs,
  .text-mb-xs {
    font-size: max(14px, 1.45vw) !important;
  }
  .text-xxs,
  .text-mb-xxs {
    font-size: max(12px, 1.1vw) !important
  }
  .text-xxxs,
  .text-mb-xxxs {
    font-size: max(10px, .95vw) !important;
  }
}


@media screen and (max-width: 1023px) {
  .text-huge, .text-mb-huge {
    font-size: 9vw !important;
  }
  .text-big, .text-mb-big {
    font-size: 9vw !important;
  }
  .text-xxxl,
  .text-mb-xxxl {
    font-size: 6vw !important;
  }

  .text-xxl,
  .text-mb-xxl {
    font-size: 4.7vw !important
  }

  .text-xl,
  .text-mb-xl {
    font-size: 4vw !important
  }

  .text-lg,
  .text-mb-lg {
    font-size: 3.2vw !important
  }

  .text-md,
  .text-mb-md {
    font-size: 2.4vw !important
  }

  .text-sm,
  .text-mb-sm {
    font-size: max(16px, 2vw) !important
  }

  .text-xs,
  .text-mb-xs {
    font-size: max(14px, 1.85vw) !important;
  }

  .text-xxs,
  .text-mb-xxs {
    font-size: max(12px, 1.45vw) !important
  }
  .text-xxxs,
  .text-mb-xxxs {
    font-size: max(10px, 1.25vw) !important;
  }
}

@media screen and (max-width: 799px) {
  .text-huge, .text-mb-huge {
    font-size: 13vw !important;
  }
  .text-big, .text-mb-big {
    font-size: 11vw !important;
  }
  .text-xxxl,
  .text-mb-xxxl {
    font-size: 8vw !important;
  }

  .text-xxl,
  .text-mb-xxl {
    font-size: 6vw !important
  }

  .text-xl,
  .text-mb-xl {
    font-size: 4.6vw !important
  }

  .text-lg,
  .text-mb-lg {
    font-size: 3.8vw !important
  }

  .text-md,
  .text-mb-md {
    font-size: 3vw !important
  }

  .text-sm,
  .text-mb-sm {
    font-size: max(16px, 2.6vw) !important
  }

  .text-xs,
  .text-mb-xs {
    font-size: max(14px, 2.2vw) !important;
  }

  .text-xxs,
  .text-mb-xxs {
    font-size: max(12px, 1.8vw) !important
  }
  .text-xxxs,
  .text-mb-xxxs {
    font-size: max(10px, 1.4vw) !important;
  }
}

@media screen and (max-width: 599px) {
  .text-huge, .text-mb-huge {
    font-size: 14vw !important;
  }
  .text-big, .text-mb-big {
    font-size: 12vw !important;
  }
  .text-xxxl,
  .text-mb-xxxl {
    font-size: 9vw !important;
  }

  .text-xxl,
  .text-mb-xxl {
    font-size: 7vw !important
  }

  .text-xl,
  .text-mb-xl {
    font-size: 5vw !important
  }

  .text-lg,
  .text-mb-lg {
    font-size: 4.25vw !important
  }

  .text-md,
  .text-mb-md {
    font-size: 3.65vw !important
  }

  .text-sm,
  .text-mb-sm {
    font-size: max(16px, 3.2vw) !important
  }

  .text-xs,
  .text-mb-xs {
    font-size: max(14px, 2.95vw) !important;
  }

  .text-xxs,
  .text-mb-xxs {
    font-size: max(12px, 2.65vw) !important
  }
  .text-xxxs,
  .text-mb-xxxs {
    font-size: max(10px, 2.15vw) !important;
  }
}

@media screen and (max-width: 450px) {
  .text-huge {
    font-size: 14vw !important;
  }
  .text-big {
    font-size: 13vw !important;
  }
  .text-xxxl,
  .text-mb-xxxl {
    font-size: 12vw !important;
  }

  .text-xxl,
  .text-mb-xxl {
    font-size: 10vw !important
  }

  .text-xl,
  .text-mb-xl {
    font-size: 7.5vw !important
  }

  .text-lg,
  .text-mb-lg {
    font-size: 5.8vw !important
  }

  .text-md,
  .text-mb-md {
    font-size: 4.25vw !important
  }

  .text-sm,
  .text-mb-sm {
    font-size: max(16px, 3.85vw) !important
  }

  .text-xs,
  .text-mb-xs {
    font-size: max(14px, 3.4vw) !important;
  }

  .text-xxs,
  .text-mb-xxs {
    font-size: max(12px, 3.2vw) !important
  }
  .text-xxxs,
  .text-mb-xxxs {
    font-size: max(10px, 2.65vw) !important;
  }
}

:root {
  --alt-font: 'VisbyRound', 'sans-serif';
  --main-font: 'VisbyCF', 'serif';
  --text-huge: 6vw;
  --text-big: 5vw;
  --text-xxxl: 4vw;
  --text-xxl: 3vw;
  --text-xl: 2.3vw;
  --text-lg: 1.7vw;
  --text-md: 1.5vw;
  --text-sm: 1.25vw;
  --text-xs: 1vw;
  --text-xxs: .75vw;
  --text-xxxs: .65vw;
  @media screen and (min-width: 1700px) {
    --text-huge: 5.5vw;
    --text-big: 4.5vw
  }
  @media screen and (min-width: 2200px) {
    --text-huge: 4vw;
    --text-big: 3.5vw;
  }
  @media screen and (max-width: 1330px) {
    --text-huge: 7vw;
    --text-big: 5.5vw;
    --text-xxxl: 4.5vw;
    --text-xxl: 3.4vw;
    --text-xl: 2.68vw;
    --text-lg: 2.2vw;
    --text-md: 1.75vw;
    --text-sm: 1.6vw;
    --text-xs: 1.45vw;
    --text-xxs: 1.1vw;
    --text-xxxs: .95vw;
  }
  @media screen and (max-width: 1023px) {
    --text-huge: 9vw;
    --text-big: 9vw;
    --text-xxxl: 6vw;
    --text-xxl: 4.7vw;
    --text-xl: 4vw;
    --text-lg: 3.2vw;
    --text-md: 2.4vw;
    --text-sm: 2vw;
    --text-xs: 1.85vw;
    --text-xxs: 1.45vw;
    --text-xxxs: 1.25vw;
  }
  @media screen and (max-width: 799px) {
    --text-huge: 13vw;
    --text-big: 11vw;
    --text-xxxl: 8vw;
    --text-xxl: 6vw;
    --text-xl: 4.6vw;
    --text-lg: 3.8vw;
    --text-md: 3vw;
    --text-sm: 2.6vw;
    --text-xs: max(14px, 2.2vw);
    --text-xxs: max(12px, 1.8vw);
    --text-xxxs: max(10px, 1.4vw);
  }
  @media screen and (max-width: 599px) {
    --text-huge: 14vw;
    --text-big: 12vw;
    --text-xxxl: 9vw;
    --text-xxl: 7vw;
    --text-xl: 5vw;
    --text-lg: 4.25vw;
    --text-md: 3.65vw;
    --text-sm: max(15px, 3.2vw);
    --text-xs: max(14px, 2.95vw);
    --text-xxs: max(12px, 2.65vw);
    --text-xxxs: 2.15vw;
  }
  @media screen and (max-width: 450px) {
    --text-huge: 14vw;
    --text-big: 13vw;
    --text-xxxl: 12vw;
    --text-xxl: 10vw;
    --text-xl: 7.5vw;
    --text-lg: max(18px, 5.8vw);
    --text-md: max(16px, 4.25vw);
    --text-sm: max(15px, 3.85vw);
    --text-xs: max(14px, 3.4vw);
    --text-xxs: max(12px, 3.2vw);
    --text-xxxs: max(10px, 2.65vw);
  }
}
