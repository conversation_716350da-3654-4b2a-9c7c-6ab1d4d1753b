import {AnyObj, ParamsOpts} from './types';
import {computed, ComputedRef, watch as vWatch, ref, Ref} from 'vue';
import {_get} from 'symbol-syntax-utils';
import {StoreGeneric} from 'pinia';

export declare interface FindOpts {
    params: ParamsOpts | ComputedRef<ParamsOpts | null>
    store: StoreGeneric,
    paginateApi?: 'hybrid' | 'client' | 'server',
    immediate?: boolean,
    watch?: boolean,
    fetchPages?: number,
    name?: string,
    log?: boolean,
    qid?: string,
    limit?: Ref<number> | ComputedRef<number>,
    skip?: Ref<number> | ComputedRef<number>,
    pause?: Ref<boolean> | ComputedRef<boolean>,
}

export const HFind = <T>(
    {
        store,
        params = ref({query: {deleted: {$ne: true}}}),
        limit = ref(5),
        skip = ref(0),
        paginateApi = 'hybrid',
        immediate = true,
        watch = true,
        log = false,
        qid,
        name,
        fetchPages = 10,
        pause
    }: FindOpts) => {


    const $limit = ref(limit.value);
    const $skip = ref(skip.value)
    const p = computed(() => {
        return {
            ...params.value,
            query: {...params.value?.query, $limit: limit.value, $skip: skip.value},
            qid: qid || store?.name || 'unknownQid'
        }
    });
    const paws = computed(() => pause?.value);

    const h$ = store.useFind(
        p,
        {
            paginateOn: paginateApi,
            pagination: {$limit, $skip},
            watch,
            immediate: true,
            queryWhen: () => !paws.value
        });

    const {
        items,
        // ids,
        // queriedAt,
        // queryState
    } = h$?.currentQuery || {items: []}

    const useItems: ComputedRef<Array<T>> = computed(() => {
        // return (_get(h$, 'allData') || []) as Array<T>
        return (_get(h$.currentQuery, 'items') || _get(h$.latestQuery, 'items') || []) as Array<T>
    })

    const usePagination = computed(() => {
        const total = h$?.total || _get(h$.latestQuery, 'total');
        const limit = h$?.limit || _get(h$.latestQuery, '$limit');
        const skip = h$?.skip || _get(h$.latestQuery, '$skip') || 0;
        return {
            total,
            limit,
            skip,
            pageCount: h$?.pageCount || Math.ceil(total / limit),
            currentPage: h$?.currentPage || Math.floor((skip || 1) / limit) + 1
        }
    })

    const serverSkip = ref(0);
    const allLimit = computed(() => (h$?.limit || limit) + (serverSkip.value * fetchPages));
    const serverData = ref({limit: allLimit.value, skip: 0, data: [], total: 0});


    const initiated = ref(false);
    const init = async (run?: boolean) => {
        if (!paws.value && (immediate || run || initiated.value)) {
            initiated.value = true;
            const pms = {
                ...p.value,
                query: {
                    ...p.value.query,
                    $limit: paginateApi === 'server' ? allLimit.value : limit.value || h$.limit,
                    $skip: serverSkip.value
                }
            };
            if (log) console.log('init', name, run, immediate, initiated.value, pms, paginateApi);

            if (paginateApi !== 'server') {
                // serverData.value = await h$.find(pms);
                serverData.value = await store.find(pms);
                if(log) console.log('got server data', serverData.value);
            }
            else serverData.value = await store.find(pms);
        }
    }

    vWatch(paws, (nv, ov) => {
        if(!nv && ov) init(true)
    }, { immediate: true })

    const cp = computed(() => {
        return h$.currentPage;
    })

    vWatch(cp, async (nv: number) => {
        if (paginateApi === 'client' && (nv * (h$?.limit || limit)) > allLimit.value) {
            serverSkip.value += (h$?.limit || limit) * fetchPages;
            await init()
        }
    }, {immediate: true})

    if (immediate && !paws.value) {
        if (log) console.log('immediate init', name);
        init(true)
    }
    // vWatch(p, (nv, ov) => {
    //     const nq = nv?.query ? JSON.stringify(nv.query || '') : undefined;
    //     const oq = ov?.query ? JSON.stringify(ov?.query || '') : undefined;
    //     if (nq !== oq && !h$.isFindPending) {
    //         console.log('compare change');
    //         init();
    //     }
    // }, {immediate: true});

    vWatch(limit, (nv, ov) => {
        if ((nv || nv === 0) && nv !== ov) {
            $limit.value = nv
        }
    }, {immediate: true})

    return {
        h$,
        clientLimit: $limit,
        init,
        initiated,
        serverSkip,
        allLimit,
        serverData,
        params: p,
        items: useItems,
        isWatching: watch,
        pagination: usePagination,
        rawItems: items,
        pageRecordCount: computed(() => Math.min(h$.total, ($skip.value || 1) * $limit.value))
    };
};

type InfiniteScrollOptions = {
    h$: { limit: number, data: Array<any>, total: number } & AnyObj,
    prefix?: string,
    containerId?: string,
    loadNum?: number
}
export const hInfiniteScroll = ({h$, prefix = 'i_s', containerId = 'I_S', loadNum = 20}: InfiniteScrollOptions) => {

    const el: Ref<any> = ref(null);
    const last: Ref<any> = ref(null);
    const lastTop = ref(7000);
    const to = ref(false);

    const hasMore = computed(() => h$.total > h$.data?.length);
    const senseScrollLoad = () => {
        if (hasMore.value) {
            if (!el.value) el.value = document.getElementById(containerId);
            // console.log('sense scroll load');
            if (!last.value) {
                const data = h$.data || []
                const l = data[data.length - 1];
                if (l) last.value = document.getElementById(`${prefix}-${data.length - 1}`);
                // console.log('setting last', last.value);

            }
            if (last.value) {
                // console.log('last', last.value);

                const boxBottom = el.value.getBoundingClientRect().bottom;
                const itemTop = last.value.getBoundingClientRect().top;
                // console.log('boxBottom: ', boxBottom);
                // console.log('itemTop: ', itemTop);
                // console.log('lastTop: ', lastTop.value);
                // console.log('pagination', pagination.value);

                if (lastTop.value > itemTop && itemTop < boxBottom) {
                    if (!to.value) {
                        h$.limit = h$.limit + loadNum
                        to.value = true;
                        setTimeout(() => {
                            to.value = false;
                        }, 2000);
                    }
                }
                lastTop.value = itemTop;
            }
        }
    }
    const addListener = () => {
        const el1 = document.getElementById(containerId);
        // console.log('got el1', el1)
        if (el1) {
            el.value = el1;
            el1.addEventListener('scroll', () => {
                senseScrollLoad()
            });
        }
    }

    return {addListener, senseScrollLoad, hasMore}
}

