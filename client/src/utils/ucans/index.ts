import { EdKeypair, verify, encode, build, Ucan, Capability, Fact, DidableKey, ValidateOptions, validate, parse, UcanParts } from '@ucans/ucans';

export * from './capabilities';

export type { Ucan, Capability, Superuser, Ability, ResourcePointer } from '@ucans/ucans';
export { SUPERUSER } from '@ucans/ucans';

export type CapabilityParts = Partial<Capability> | [string, Array<string> | string];

export const genKeyPair = async ({ decode = true }: { decode?:boolean}):Promise<EdKeypair|string> => {
    const kp = await EdKeypair.create({ exportable: decode });
    if (kp) {
        if (decode) {
            return await kp.export();
        } else return kp;
    } else throw new Error('Error generating keypair');
};

export const encodeKeyPair = ({ secretKey }: { secretKey: string }) => {
    return EdKeypair.fromSecretKey(secretKey);
};

export const validateUcan = async (encodedUcan:string, options?:Partial<ValidateOptions>) => {
    return await validate(encodedUcan, options);
};

type VerifyOptions = {
    audience: string,
    encodeUcan?: boolean,
    requiredCapabilities?: Array<{ capability: Capability, rootIssuer: string }>
}

export declare type VerifyResult<Ok, Err = Error> = {
    ok: true;
    value: Ok;
} | {
    ok: false;
    error: Err;
};
export const verifyUcan = async (ucan:string, {audience, requiredCapabilities = []}:VerifyOptions):Promise<VerifyResult<any, any>> => {
    if(ucan && audience) {
        return await verify(ucan, {
            audience,
            // TODO: write isRevoked callback
            isRevoked: async (ucan:any) => false,
            // as a stub. Should look up the UCAN CID in a DB.
            // isRevoked: ucan => some function to determine if revoked
            requiredCapabilities
        })
            .catch((err:Error) => {
                console.log(`Error verifying ucan ${ucan} for audience ${audience}:  ${err.message}`);
                return { ok: false, error: err }
            })
    } else {
        console.error(`Error verifying ucan ${ucan} for audience ${audience}`);
        return Promise.resolve({ ok: false, error: { name: '500', message: 'Unknown error'} })
    }
};

export type UcanBuildParams = {
    issuer: DidableKey;
    audience: string;
    capabilities?: Capability[];
    lifetimeInSeconds?: number;
    expiration?: number;
    notBefore?: number;
    facts?: Fact[];
    proofs?: string[];
    addNonce?: boolean;
}

export const buildUcan = async (config:UcanBuildParams):Promise<Ucan> => {
    if(!config.lifetimeInSeconds) config.lifetimeInSeconds = (60 * 60 * 24);
    return await build(config)
        .catch((err:any) => {
            throw new Error(`error building ucan:  ${err.message}`);
        });
};


export const ucanToken = (ucan:Ucan|string):string => {
    return typeof ucan === 'string' ? ucan : encode(ucan);
};

export const parseUcan = (ucan:Ucan|string):UcanParts => {
    return parse(ucanToken(ucan));
}

export const defaultScheme = 'symbolDb';
export const hierPartBase = 'commoncare';

export const defaultHierPartExtension = '*'
export const defaultHierPart = `${hierPartBase}/${defaultHierPartExtension}`;


type CapabilityGen = {
    with: { scheme:string, hierPart:string },
    can: { namespace:string, segments:string[]|string}
};

export const genCapability = (capabilityParts: Partial<Capability>): Capability => {
    let namespace = '';
    let segments:string[] = [];
    if(typeof capabilityParts.can === 'object'){
        namespace = capabilityParts.can.namespace;
        segments = capabilityParts.can.segments;
    }
    const { scheme = defaultScheme, hierPart = defaultHierPart } = {scheme:defaultScheme, hierPart:defaultHierPart, ...capabilityParts.with };
    return {
        with: { scheme, hierPart: `${hierPart || defaultHierPart}`},
        can: typeof capabilityParts.can === 'string' ? capabilityParts.can : { namespace, segments: Array.isArray(segments) ? segments : [segments] }
    }
}
export const simpleCapability = (cap: [string, string | string[]]):Capability => {
    return genCapability({with: {scheme: defaultScheme, hierPart: defaultHierPart}, can: {namespace: cap[0], segments: typeof cap[1] === 'string' ? [cap[1]] : cap[1]}})
};


