<template>
  <q-page class="__poolspage">
    <div class="row justify-center __pr">
      <div class="_cent">
        <div class="row">
          <div class="col-12 col-md-6">
            <div class="_fw __tx">
              <div class="text-big text-weight-bolder _l1-2">It's nice to share risk.</div>
              <div class="text-md __pt text-weight-bold alt-font">The only question is:</div>
              <div class="text-xxl text-mb-lg text-weight-bolder q-pt-md">Who makes the rules?</div>
              <q-separator dark class="q-my-md"></q-separator>
              <div class="text-md alt-font text-weight-bold __sh">Effective risk sharing requires agreement about the
                conduct of the participants. Few groups are exercising the rights afforded to them
                under the ACA to choose their risk pool - and the default pool is bottomless.
              </div>
            </div>
          </div>
          <div class="col-12 col-md-6"></div>
        </div>
      </div>
    </div>

    <div class="pd12 _fw row justify-center">
      <div class="_cent q-pt-xl">

        <div class="row justify-center">
          <div class="_sent q-pa-md">

            <div class="text-center text-xxl text-weight-bolder">So, we're all in a public pool</div>
            <div class="text-center text-md">You might not like what people do in the water.</div>

          </div>
        </div>

        <div class="row items-center">

          <div class="col-12 col-md-6 q-pa-md">
            <div class="_fw" v-for="(item, i) in items" :key="`item-${i}`">
              <q-expansion-item expand-icon="mdi-menu-down" :model-value="active === i"
                                @update:model-value="active === i ? active = -1 : active = i">
                <template v-slot:header>
                  <div class="_fw q-py-md">
                    <div class="text-sm q-mb-xs text-secondary text-weight-bolder alt-font">{{ item.label }}</div>
                    <div class="text-lg _l1 text-weight-bold">{{ item.title }}</div>
                    <div class="text-sm text-weight-medium">{{ item.caption }}</div>
                  </div>
                </template>
                <div class="text-xs q-pa-md" v-html="item.text"></div>
              </q-expansion-item>

              <q-separator v-if="i < 2"></q-separator>
            </div>
          </div>

          <div class="col-12 col-md-6 q-pa-md">
            <div class="row justify-center q-pa-md">
              <q-img :src="pool" style="width: 100%;max-width: 600px;" fit="contain"></q-img>
            </div>
          </div>

        </div>

      </div>
    </div>

    <div class="q-py-xl _fw row justify-center">
      <div class="_cent q-py-md">
        <div class="row items-center">
          <div class="col-12 col-md-6 q-pa-md">

            <div class="text-xxl text-weight-bolder">Here's what can be done about it</div>
            <div class="text-sm q-pb-lg">A process to move both risk and opportunity as locally as possible</div>
            <q-card class="__vid q-my-md">
              <iframe width="100%" height="100%" src="https://www.youtube.com/embed/JsLwqRElFe0?si=RHbM1vw3WUzW1-az"
                      title="YouTube video player" frameborder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                      allowfullscreen></iframe>
            </q-card>
          </div>
          <div class="col-12 col-md-6 q-pa-md">
            <div class="_fw">
              <div class="__tli" v-for="(thing, i) in things" :key="`thing-${i}`">
                <q-expansion-item expand-icon="mdi-menu-down">
                  <template v-slot:header>
                    <div class="_fw">
                      <div class="text-xs text-weight-bolder alt-font">
                        <div>{{ thing.year }}</div>
                      </div>
                      <div class="text-md text-weight-bold" v-html="thing.title"></div>
                    </div>
                  </template>
                  <div class="text-xs" v-html="thing.text"></div>
                </q-expansion-item>

              </div>
            </div>
          </div>
        </div>


      </div>
    </div>

    <div class="pd5 row justify-center">
      <div class="_cent">
        <div class="row items-center">
          <div class="col-12 col-md-6 q-pa-md">

            <div class="text-xxl text-weight-bolder q-pb-lg text-center">
              How to build a private risk pool
            </div>

            <q-img fit="contain" class="__drown" :src="drowning"></q-img>

          </div>
          <div class="col-12 col-md-6 q-pa-lg q-pa-sm-md">
            <q-tabs align="left" active-color="primary" v-model="tab" dense>
              <q-tab
                  v-for="(step, i) in tabs" :key="`step-${i}`"
                  no-caps
                  class="alt-font text-weight-bold"
                  flat
                  :name="i"
              >
                <div class="flex items-center">
                  <span>Step</span>
                  <q-icon class="q-ml-sm" size="20px" :name="`mdi-numeric-${i+1}-circle`"></q-icon>
                </div>
              </q-tab>
            </q-tabs>
            <q-tab-panels class="_panel" v-model="tab" animated transition-prev="slide-right"
                          transition-next="slide-left" transition-duration="800">
              <q-tab-panel
                  v-for="(t, i) in tabs" :key="`tab-${i}`"
                  class="_panel"
                  :name="i"
              >
                <div class="font-2r text-weight-bolder q-pt-lg q-pb-md">{{ t.label }}</div>
                <div class="font-1-1-4r text-weight-medium" v-html="t.text"></div>
              </q-tab-panel>
            </q-tab-panels>
            <!--            <div class="row justify-end q-py-md">-->
            <!--              <q-btn-->
            <!--                -->
            <!--              ></q-btn>-->
            <!--            </div>-->
          </div>
        </div>
      </div>
    </div>

    <div class="_fw __brown">
      <div class="__blob"></div>
      <div class="__blob2"></div>
      <div class="row justify-center z2">
        <div class="_cent">
          <div class="row justify-center">
            <div class="__tt q-pa-md">
              <div class="text-center text-xxl"><span class="text-lg alt-font text-uppercase text-weight-bolder text-p9">The key:</span>
                <span class="text-p9 alt-font text-weight-bolder">&nbsp;Pool Hosts</span></div>
              <div class="text-md text-center text-weight-bold alt-font">Reform by voluntary and private cooperation
                requires organizers and
                connectors
              </div>
            </div>
          </div>
<!--          <div class="row">-->
<!--            <div class="col-12 col-md-6 q-pa-md">-->

<!--              <div class="_fw">-->
<!--                <div class="row q-pt-md">-->
<!--                  <q-img style="height: 50px; width: 50px;" fit="contain" :src="icon"></q-img>-->
<!--                </div>-->
<!--                <div class="alt-font text-weight-bolder text-uppercase text-lg">Our Job</div>-->
<!--                <div v-for="(job, i) in jobs" :key="`job-${i}`" class="__jobs bg-white text-black">-->
<!--                  {{ job }}-->
<!--                </div>-->
<!--              </div>-->

<!--            </div>-->
<!--            <div class="col-12 col-md-6 q-pa-md">-->

<!--              <div class="_fw">-->
<!--                <div class="row q-pt-md">-->
<!--                  <div class="__hostgo flex flex-center">-->
<!--                    <div class="__b"></div>-->
<!--                  </div>-->
<!--                </div>-->
<!--                <div class="alt-font text-weight-bolder text-uppercase text-lg text-p9">Hosts' Job</div>-->
<!--                <div v-for="(job, i) in hostJobs" :key="`host-job-${i}`" class="__jobs bg-p9 text-white">-->
<!--                  {{ job }}-->
<!--                </div>-->
<!--              </div>-->

<!--            </div>-->
<!--          </div>-->
        </div>
      </div>
    </div>

    <div class="__c">
      <div class="row justify-center">
        <div class="_cent">

          <div class="row items-center">

            <div class="col-12 col-md-6 q-pa-lg">

              <div>
                <div class="text-weight-bolder alt-font text-md _l0-5">You could host as
                  a{{ hostTypes[activeType].extension || '' }}
                </div>
                <div class="_l0-5 flex items-center __hb cursor-pointer">
                  <span :class="`__hw text-primary text-weight-bolder text-xl ${activeType > -1 ? '' : '__hwo'}`">
                    {{ hostTypes[activeType].label }}&nbsp;
                  </span>
                  <q-icon size="20px" name="mdi-menu-down"></q-icon>

                  <q-menu>
                    <q-card class="q-pa-md">
                      <q-item v-for="(type, i) in hostTypes" :key="`type-${i}`" clickable @click="setActive(i, true)">
                        <!--                        <q-item-section avatar>-->
                        <!--                          <q-icon :name="type.icon"></q-icon>-->
                        <!--                        </q-item-section>-->
                        <q-item-section>
                          <q-item-label class="font-1r text-weight-bold">{{ type.label }}</q-item-label>
                        </q-item-section>
                      </q-item>
                    </q-card>
                  </q-menu>

                </div>
              </div>

              <div :class="`text-sm alt-font __hw ${activeType > -1 ? '' : '__hwt'}`">
                {{ hostTypes[activeType].text }}
              </div>
              <q-separator class="q-my-md"></q-separator>
              <div class="text-sm text-weight-bolder alt-font __p9">Our model hinges on having 1000
                hosts run 1000 experiments - and sharing what works best.
              </div>

            </div>

            <div class="col-12 col-md-6 q-pa-md">
              <div class="_fw __cr text-p9">
                <div class="text-weight-bold text-md">How about a little transparency?</div>
                <div class="text-xxl text-weight-bolder alt-font">
                  Hosts determine their own fees - which are disclosed. No commissions, just fee for service.
                </div>
              </div>
            </div>

          </div>

        </div>
      </div>

    </div>

    <div class="pd8 _fw">
      <div class="row justify-center">
        <div class="_cent">
          <reach-out
              bg="transparent"
              title="Become a Host"
              caption="We can change healthcare together"
          ></reach-out>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import {computed, onMounted, ref} from 'vue';
  import pool from 'src/assets/comics/pee_pool.png';
  import ReachOut from 'src/components/common/contact/ReachOut.vue';
  import drowning from 'src/assets/comics/drowning.png';

  const activeType = ref(0);

  let t;
  const pause = ref(9000);
  const setActive = (i, ps) => {
    if (ps) {
      clearTimeout(t);
      t = null;
      pause.value = 0;
    }
    t = setTimeout(() => {
      if (!i && i !== 0) {
        i = activeType.value < hostTypes.value.length - 1 ? activeType.value + 1 : 0
      }
      if (ps) pause.value = 60000;
      else pause.value = 9000;
      activeType.value = i;
      setActive();
    }, pause.value);
  }

  const tab = ref(0);
  const tabs = ref([
    {
      label: 'Restructure your group plan',
      text: 'Define what the plan <b>must</b> do (de-risk everything else). Define what the plan <b>can</b> do. Define who pays.<div class="q-pt-sm">After that you just optimize for taxes and decide <b>if</b> you need insurance to contain the risks in your plan.</div><div class="q-pt-sm">This sort of structure will prevent everyone from being forced into the same risk pool even within your group. It can be done. Define what <b>must</b> be offered under the law - then build in opportunity through options.</div>'
    },
    {
      label: 'Track your actuarial data',
      text: 'This is the most wildly untapped resource in this space. Groups are everywhere between not tracking much data at all, to letting insurance companies have it all.<div class="q-pt-sm">When you track your usage data, you build definitions of actuarial value for just your group - which we can use to optimize your plan among all CommonCare groups.</div><div class="q-pt-sm">This is why we track everything, record-keep in compliance with HIPAA, and provide you atomic usage stats that we can use to grow your private healthcare experience together. </div>'
    },
    {
      label: 'Iteratively improve your pool',
      text: 'With the right structure and administration in place, you can start cutting the remaining fat effortlessly.<div class="q-pt-sm">We provide tools for working directly with providers, negotiating compounding better rates, and teaming up with other similar groups for more buying power.</div><div class="q-pt-sm">We can\'t change the whole experience in a day, but iteratively we can move to a whole new care experience (and cost) in just a few years time.</div>'
    }
  ])

  const active = ref(-1);
  const items = computed(() => {
    return [
      {
        label: 'Pool 1: Uncle Sam\'s Pool Party',
        title: 'ACA Marketplaces',
        caption: 'Limitless risk',
        text: '<div>State run healthcare marketplaces are officially labeled "competitive marketplaces" ... because the definition of competitive is mandated and state run 😅.</div> <div class="q-pt-sm">With no pre-existing condition exclusions, no behavior-based premium adjustments, and no annual or lifetime dollar limits, and government subsidies you only get if your income is low enough - you get what you see if you go shopping on the state exchange.</div>'
      },
      {
        label: 'Pool 2: The Industrial Complex',
        title: 'Health Insurance Companies',
        caption: 'Looking nothing like insurance',
        text: '<div>With dictates from the state and federal government regarding what health insurance must look like (which looks nothing like insurance) - this is a pseudo private solution to risk management in this space.</div><div class="q-pt-sm">For private group coverage, your actual claims experience can affect premium rates - but, at best, this setup lacks transparency. Especially since the premiums are typically only nominally better for efficient groups. </div>'
      },
      {
        label: 'Pool 3: The Key to Freedom',
        title: 'Group Sponsors',
        caption: 'Your pool, your rules... almost',
        text: '<div>There is a path for group sponsors to create their own actuarial definitions in the ACA. Yet, most groups are letting insurance companies write their plan documents.</div><div class="q-pt-sm">Applicable large employers still have to meet the minimum value rules, but allowing an insurance company to define what that means is a forfeiture of nearly every advantage offered to them.</div>'
      }
    ];
  })

  const things = computed(() => {
    return [
      {
        year: '2024',
        title: '<span>First: <span class="text-primary alt-font">Better Group Plans</span></span>',
        text: '<div>Our process for creating better structure for group health plans means less tax, less insurance premiums, and less waste. CommonCare Groups also start building data and a network of providers.</div><div class="q-pt-sm">As our groups improve their plan structure, plan participants are more accountable and more rewarded for their consumer decisions.</div>'
      },
      {
        year: '2025',
        title: '<span>Next: <span class="text-primary alt-font">Captive Pools</span></span>',
        text: '<div>As groups begin gathering quality data, we can match-make cooperative groups into captive pools to supercharge their experience.</div><div class="q-pt-sm">Why? For buying power, risk sharing, and security. Many groups with similar desired outcomes and practices makes for a powerful consuming body - totally voluntary and private.</div>'
      },
      {
        year: '2027',
        title: '<span>Finally: <span class="text-primary alt-font">Federated Care</span></span>',
        text: '<div>With enough relationships and data history, we can carve out actuarial pools with enough data to purchase the pool\'s provider infrastructure outright.</div><div class="q-pt-sm">That means private docs and facilities tailored to the needs of each pool. Friction defines many of the challenges in care today. This model is nearly frictionless and totally private.</div>'
      }
    ];
  })

  const jobs = computed(() => {
    return [
      'Accessible open-source plan structure and data benchmarks',
      'AI models for administration and plan design',
      'Turn-key services for battling today\'s infrastructure',
      'Cutting edge software for organizing care pools',
      'Producing educational materials, content, and research'
    ]
  })

  const hostJobs = computed(() => {
    return [
      'Recruit and onboard groups, providers, and partners',
      'Manage education and communication distribution',
      'Create network solutions for complimentary services',
      'Monitor plans against the CommonData metrics',
      'Organize Care Pools from the data recommended groups'
    ]
  })

  const hostTypes = ref([
    {
      label: 'Consultant',
      text: 'The second biggest expense for employers in this country is a BIG deal. Providing a ground breaking solution for an unbeatable price is good business.'
    },
    {
      label: 'Accountant',
      extension: 'n',
      text: 'Nobody is in a higher trust position with more client data than accountants. No sector receives more tax advantage than healthcare. Ensure your clients get it right, grow your network, expand a new profit center.'
    },
    {
      label: 'Doctor',
      text: 'What!? Approach this from the provider side? Oh yes! We think this is the most dynamite host opportunity of all. Get involved in the economic side of healthcare at the source of funding - and grow your own pool of payers.'
    },
    {
      label: 'Connector',
      text: 'Beyond all doubt, the best connectors will win this model. We provide the expertise and structure needed for a non expert to come in and build a serious network. This is about organizing people - we\'ll be the healthcare experts in the room.'
    },
    {
      label: 'Association',
      extension: 'n',
      text: 'Associations want to solve member problems, but always worry about promoting products to their membership unscrupulously. We can help you solve one of the biggest problems your members face - with conscionable and transparent fees that they\'ll never beat.'
    }
  ])

  onMounted(() => {
    setActive();
  });
</script>

<style lang="scss" scoped>
  .__poolspage {
    min-height: 90vh;
  }

  .__pr {
    background-image: url('src/assets/rich_government.png');
    background-size: cover;
    background-position: right;
  }

  .__pt {
    //color: #0E0505;
    color: black;
  }

  .__sh {
    text-shadow: .5px .5px #0E0505;
  }

  .__tx {
    background: transparent;
    padding: 12vh 20px;
    color: white;
  }

  .__tt {
    width: 800px;
    max-width: 100%;
  }

  .__brown {
    overflow: hidden;
    z-index: 1;
    background: var(--q-p2);
    padding: 10vh 0;
    position: relative;
  }

  .__blob {
    position: absolute;
    z-index: -1;
    width: 155vw;
    height: 140vw;
    border-radius: 50%;
    opacity: .6;
    background: radial-gradient(var(--q-p7) -50%, transparent 50%);
    animation: roam 40s infinite;
    top: -80vw;
    left: -60vw;
    transform: translate(5%, 0);
  }

  .__blob2 {
    position: absolute;
    z-index: -1;
    width: 1500px;
    height: 2000px;
    border-radius: 50%;
    opacity: .6;
    background: radial-gradient(var(--q-p7) -20%, transparent 50%);
    animation: roam_m 40s infinite;
    bottom: -20vw;
    right: -30vw;
    transform: translate(5%, 0);
  }

  .__drown {
    width: 600px;
    max-width: 100%;
    height: 411px;
    max-height: 65vw;
  }


  .__hostgo {
    height: 50px;
    width: 50px;

    .__b {
      height: 50px;
      width: 50px;
      border-radius: 50%;
      border: solid 10px var(--q-p9);
    }
  }

  .__jobs {
    z-index: 2;
    width: 100%;
    border-radius: 10px;
    box-shadow: 0px 9px 18px -7px rgba(0, 0, 0, .6);
    padding: 20px;
    margin: 10px 0;
    font-weight: 600;
    font-size: 1.2rem;
    text-align: center;
  }

  .__c {
    padding: 8vh 0;
  }

  .__vid {
    width: 560px;
    max-width: 90vw;
    height: 315px;
    max-height: 51vw;
    overflow: hidden;
    border-radius: 10px;
  }

  .__tli {
    width: 100%;
    border-radius: 12px;
    background: white;
    //box-shadow: 0px 8px 18px -9px #9e9e9e;
    padding: 20px 20px;
    margin: 10px 0;
  }

  .__hb {
    width: auto;
    height: 60px;
    overflow-y: hidden;
  }

  .__hw {
    transform: none;
    transition: all .4s;
    opacity: 1;
  }

  .__hwo {
    transform: translate(0, 500%);
  }

  .__hwt {
    opacity: 0;
  }

  .__cr {
    padding: 40px 30px;
    border-radius: 15px;
    box-shadow: 0 2px 18px -9px var(--q-primary);
    border: solid 2px var(--q-p9)
  }


  @media screen and (max-width: 1023px) {
    .__tx {
      background: rgba(0, 0, 0, .5);
    }

    .__pt {
      display: none;
    }
    .__sh {
      text-shadow: none;
    }

  }
</style>
