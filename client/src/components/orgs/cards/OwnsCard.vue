<template>
  <div class="_fw">
    <slot name="top" :org="item" :add="() => addDialog = true">
      <div class="flex items-center">
        <q-chip outline square :label="$capitalizeFirstLetter(path)" color="dark" clickable
                @click="addDialog = !addDialog"></q-chip>
        <q-btn size="sm" dense flat :icon="`mdi-${addDialog ? 'minus' : 'plus'}`" color="primary"
               @click="addDialog = !addDialog"></q-btn>
      </div>
    </slot>
    <slot name="adding" :on="addDialog === true" :add="() => addDialog = true">
      <q-slide-transition>
        <div v-if="addDialog" class="__add q-py-sm">
          <div class="row items-center">
            <div class="font-1r text-weight-medium">Add Owner</div>
          </div>
          <owner-form
              :model-value="active"
              @update:model-value="addOwner"
              :org="item"
              :path="path"
          ></owner-form>
        </div>
      </q-slide-transition>
    </slot>
    <q-list v-bind="{
      separator: true,
      dense: true,
      class: '__ownscard',
      listAttrs
    }">


      <div class="_fw" v-for="(owner, i) in ownerList" :key="`owner-${i}`" @click="handleClick(owner)">
        <slot name="item" :owner="owner">
          <default-item
              :item-attrs="{
                clickable: true
              }"
              :size-in="avatarSize"
              :model-value="owner._id ? owner : owner.id"
              :store="owner.idService === 'orgs' ? orgStore : pplStore"
          >
            <template v-slot:side="scope">
              <div class="flex items-center">
                <q-btn dense flat no-caps :label="`${owner.percent || 0}%`" class="text-weight-bold alt-font" @click="addDialog ? ownerDialog = true : addDialog = true"></q-btn>
                <q-btn v-if="addDialog" dense flat icon="mdi-minus" color="red" @click="remove = scope.item"></q-btn>
              </div>
            </template>
            <template v-slot:bottom>
              <q-item-section>
                <q-item-label caption>
                  <div class="flex items-center">
                    <div>Role:</div>
                    <q-chip color="white" :label="owner.position" size="sm" class="text-weight-bold">
                      <q-menu v-if="addDialog">
                        <div class="__menu">
                          <q-list separator>
                            <q-item v-for="(opt, idx) in options" :key="`opt-${idx}`" clickable @click="setPosition(opt.key, i)">
                              <q-item-section>
                                <q-item-label>
                                  {{opt.label}}
                                </q-item-label>
                                <q-item-label caption>
                                  {{opt.hint}}
                                </q-item-label>
                              </q-item-section>
                            </q-item>
                          </q-list>
                        </div>
                      </q-menu>
                    </q-chip>
                  </div>
                </q-item-label>
              </q-item-section>
            </template>
          </default-item>
        </slot>
      </div>
    </q-list>

    <common-dialog setting="small" :model-value="!!remove" @update:model-value="val => !val ? remove = undefined : ''">
      <div class="__crd q-pa-md">
        <div class="font-1r text-weight-bold">Remove {{remove?.name || ''}} as owner?</div>
        <div class="q-py-md row justify-end items-center">
          <q-btn flat color="black" icon-right="mdi-close" label="Cancel" @click="remove = undefined"></q-btn>
          <q-btn flat color="red" icon-right="mdi-delete" label="Yes" @click="executeRemove()"></q-btn>
        </div>
      </div>
    </common-dialog>
    <common-dialog setting="medium" v-model="ownerDialog">
<!--      <owner-graph :owners="ownerList"></owner-graph>-->
    </common-dialog>
  </div>
</template>

<script setup>
  import OwnerForm from 'src/components/orgs/cards/OwnerForm.vue';
  import DefaultItem from 'src/components/common/avatars/DefaultItem.vue';
  import CommonDialog from 'src/components/common/dialogs/CommonDialog.vue';
  // import OwnerGraph from 'src/components/orgs/control/OwnerGraph.vue';

  import { options } from '../forms/owner-titles';

  import {idGet} from 'src/utils/id-get';
  import { _get } from 'symbol-syntax-utils';
  import {computed, ref} from 'vue';
  import {$capitalizeFirstLetter} from 'src/utils/global-methods';

  import {useOrgs} from 'src/stores/orgs';
  import {usePpls} from 'stores/ppls';

  const orgStore = useOrgs();
  const pplStore = usePpls();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    org: { required: true },
    listAttrs: Object,
    size: { type: String },
    editing: Boolean,
    path: { type: String, default: 'owners' }
  });

  const active = ref(null);
  const addDialog = ref(false);
  const remove = ref(undefined);
  const ownerDialog = ref(false);

  const { item } = idGet({
    // params: computed(() => {
    //   return {
    //     query: {
    //       searchAll: {
    //         services: ['ppls', 'orgs']
    //       }
    //     }
    //   }
    // }),
    value: computed(() => props.org),
    store: orgStore,
  });

  const avatarSize = computed(() => {
    const obj = {
      'xs': '20px',
      'sm': '24px',
      'md': '32px',
      'lg': '50px',
      'xl': '60px'
    };
    return obj[props.size || 'md'];
  });

  const handleClick = (owner) => {
    active.value = item.value[props.path][item.value[props.path].map(a => a.id).indexOf(owner._id)];

    if (props.editing) {
      addDialog.value = true;
    }
  };

  const executeRemove = () => {
    const ownrs = item.value.owners.slice();
    const idx = ownrs.map(a => a.id).indexOf(remove.value._id);
    if(idx > -1){
      ownrs.splice(idx, 1);
      emit('update:model-value', ownrs);
      remove.value = undefined;
    }
  }

  const setPosition = (val, idx) => {
    const ownrs = item.value.owners.slice();
    if(idx > -1) {
      ownrs[idx].position = val;
      emit('update:model-value', ownrs);
    }
  }

  // const setPercent = (val, owner) => {
  //   const ownrs = item.value.owners.slice();
  //   const obj = {};
  //   ownrs.map(a => obj[a.id] = a.percent);
  //   const total = owners.reduce((a,b) => a.percent + b.percent, 0);
  //   const ov = Number(obj[owner.id] || 0);
  //   obj[owner.id] = val;
  //   const diff = ov - val;
  //
  //   if(ownrs.reduce((a, b) => a.percent + b.percent, 0) === );
  // }

  const ownerList = computed(() => {
    // return item.value?.owners;
    const ppls = _get(item.value, ['_fastjoin', props.path, 'ppls', 'data'], []);
    const orgs = _get(item.value, ['_fastjoin', props.path, 'orgs', 'data'], []);
    const all = [...ppls, ...orgs];
    return _get(item.value, props.path, []).map(a => {
      const data = all.filter(b => b._id === a.id)[0] || undefined
      return {...data, ...a};
    }).sort((a, b) => b.percent - a.percent);
  });

  const closeDialog = () => {
    active.value = null;
    addDialog.value = false;
  };

  const addOwner = val => {
    emit('update:model-value', [...(item.value.owners || []).filter(a => a.id !== val.id), val]);
    closeDialog();
  };
</script>

<style scoped>
  .__ownscard {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
  }

  .__add {
    width: 100%;
    padding: 10px;

  }
  .__crd {
    width: 100%;
    border-radius: 15px;
    background: white;
  }
  .__menu {
    width: 100%;
    max-width: 300px;
    border-radius: 10px;
    background: white;
  }
</style>
