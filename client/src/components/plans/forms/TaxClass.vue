<template>
  <div class="_fw relative-position">

    <q-list separator>
      <q-item-label header class="cursor-pointer">
        <span> ℹ️ Understanding tax classification</span>
        <q-popup-proxy breakpoint="5000">
          <why-two></why-two>
        </q-popup-proxy>
      </q-item-label>
      <q-item clickable @click="$emit('update:model-value', '105')">
        <q-item-section avatar>
          <q-icon :name="`mdi-checkbox-${is105 ? 'marked' : 'blank'}-outline`" :color="is105 ? 'green' : ''"></q-icon>
        </q-item-section>
        <q-item-section>
          <q-item-label class="font-1r tw-six">Section 105</q-item-label>
          <!--            <q-item-label caption class="font-1r">A plan for an employer to put funds aside to pay for employee-->
          <!--              premiums, oop expenses, or other qualified healthcare costs-->
          <!--            </q-item-label>-->
        </q-item-section>

      </q-item>

      <q-item clickable @click="$emit('update:model-value', '125')">
        <q-item-section avatar>
          <q-icon :name="`mdi-checkbox-${is125 ? 'marked' : 'blank'}-outline`" :color="is125 ? 'green' : ''"></q-icon>
        </q-item-section>
        <q-item-section>
          <q-item-label class="font-1r tw-six">
            Section 125
          </q-item-label>
          <!--            <q-item-label caption class="font-1r">A plan to offer a variety of benefits the can be purchased by-->
          <!--              employees through payroll deduction and optionally employers may contribute-->
          <!--            </q-item-label>-->
        </q-item-section>
      </q-item>

    </q-list>

  </div>
</template>

<script setup>
  import WhyTwo from 'src/components/plans/info/WhyTwo.vue';

  import {computed} from 'vue';

  const props = defineProps({
    modelValue: String
  })

  const is105 = computed(() => props.modelValue === '105');
  const is125 = computed(() => props.modelValue === '125');
</script>

<style lang="scss" scoped>

</style>
