<template>
  <div class="_fw">
    <div class="_form_grid">
      <slot name="top"></slot>
      <div class="_form_label">Single</div>
      <div class="q-pa-sm">
        <money-input prefix="$" :decimal="0" v-model="form.single" @blur="emitUp('single')"></money-input>
      </div>

      <div class="_form_label">Family</div>
      <div class="q-pa-sm">
        <money-input prefix="$" :decimal="0" v-model="form.family" @blur="emitUp('family')"></money-input>
      </div>
      <slot name="bottom"></slot>

    </div>
  </div>
</template>

<script setup>

  import {nextTick, ref, watch} from 'vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: {  required: true }
  })

  const formFn = (defs) => {
    return {
      single: 0,
      ...defs
    }
  }

  const form = ref(formFn());

  const emitUp = (path) => {
    nextTick(() => {
      emit('update:model-value', { ...props.modelValue, ...form.value }, path);
    })
  }

  watch(() => props.modelValue, (nv) => {
    if(nv) form.value = formFn(nv);
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
