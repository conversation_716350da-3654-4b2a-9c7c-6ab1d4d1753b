<template>
  <div class="_fw _form_grid">
    <div class="_form_label">Name</div>
    <div class="q-px-sm">
      <q-input placeholder="Contact Name..." v-model="form.name" @update:model-value="emitUp"></q-input>
    </div>
    <slot name="afterName"></slot>
    <div class="_form_label">Phone</div>
    <div class="q-px-sm">
      <phone-input :input-attrs="{ placeholder: 'Contact Phone...' }" emit-value option-value="number.e164" @update:model-value="emitUp" v-model="form.phone"></phone-input>
    </div>
    <div class="_form_label">Email</div>
    <div class="q-px-sm">
      <email-field hide-bottom-space placeholder="Contact Email..." v-model="form.email" @update:model-value="emitUp"></email-field>
    </div>
    <div class="_form_label">Address</div>
    <div class="q-px-sm">
      <q-input placeholder="Contact Address..." v-model="form.address" @update:model-value="emitUp"></q-input>
    </div>
  </div>
</template>

<script setup>
  import EmailField from 'components/common/input/EmailField.vue';
  import PhoneInput from 'components/common/phone/PhoneInput.vue';

  import {ref, watch} from 'vue';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: Object
  })

  const formFn = (defs) => {
    return {
      name: undefined,
      phone: undefined,
      email: undefined,
      address: undefined,
      ...defs
    }
  }

  const form = ref(formFn());

  const emitUp = () => {
    emit('update:model-value', form.value);
  }

  watch(() => props.modelValue, (nv) => {
    if(nv) form.value = formFn(nv)
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
