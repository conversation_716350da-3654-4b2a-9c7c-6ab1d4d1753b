<template>
  <div class="_fw pd3">
    <plan-contributions :group-id="$route.params.groupId" :pla-n="plan"></plan-contributions>
  </div>
</template>

<script setup>

  import {computed} from 'vue';
  import {loginPerson} from 'stores/utils/login';
  import PlanContributions from 'components/plans/scores/contributions/PlanContributions.vue';
  const { person } = loginPerson()

  const props = defineProps({
    plaN: { required: true }
  })

  const plan = computed(() => props.plaN)

</script>

<style lang="scss" scoped>

</style>
