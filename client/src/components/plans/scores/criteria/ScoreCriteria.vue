<template>
  <div class="row pd8">
    <div class="col-12 col-md-5 q-pa-md relative-position">
      <div class="__stamp">
        <q-img fit="contain" :src="watermark" class="_fa"></q-img>
      </div>
      <div class="relative-position z1">
        <div class="text-sm tw-six">THE CRITICAL</div>
        <div class="text-xl tw-six">
          4 pillars of great health plans
        </div>
        <div class="text-sm">Offering plan options people want - and at a better price than they can achieve on their own presupposes these 4 qualities.</div>
      </div>

    </div>
    <div class="col-12 col-md-7 q-py-md pw2">
      <div v-for="(m, i) in Object.keys(measures)" :key="`m-${i}`" class="__measure">
        <div class="flex items-center">
          <div>{{measures[m].label}}</div>
          <q-icon class="q-ml-sm" v-bind="measures[m].icon"></q-icon>
        </div>
        <div>{{measures[m].text}}</div>
      </div>
    </div>
  </div>
</template>

<script setup>

import watermark from 'assets/commoncare_icon_raise.svg';
import {computed} from 'vue';

const measures = computed(() => {
  return {
    'transparency': {
      icon: { name: 'mdi-magnify' },
      label: 'Transparency',
      text: 'A dollar spent on benefits (by employer or employee) is a dollar less to take home. Yet less than 5% of employees know what is spent on their benefits. Fewer than 8% of plan sponsors know the actuarial cost of their health plan - without knowing these costs you\'re guessing.',
      subs: {
        'compensation': { label: 'Compensation' },
        'actuarial': { label: 'Actuarial' },
        'admin': { label: 'Admin' },
      }
    },
    'choice': {
      icon: { name: 'mdi-list-box' },
      label: 'Choice',
      text: 'There is no good reason to trap people\'s wages in a health plan. Forfeiting compensation if a particular health insurance option doesn\'t suit you is nonsensical - unless of course the plan broker\'s commission depends on it.',
      subs: {
        'freedom': { label: 'Freedom' },
        'quality': { label: 'Quality' },
        'variety': { label: 'Variety' }
      }
    },
    'purity': {
      icon: { name: 'mdi-water' },
      label: 'Purity',
      text: 'When you need healthcare, you want direct access to a high quality physician (and/or supplies). The more a plan can facilitate this with no middle-men, the higher its value. Group health plans cut through important red tape to make this possible - if they keep the middle-men out.',
      subs: {
        'access': { label: 'Access' },
        'incentives': { label: 'Incentives' }
      }
    },
    'coverage': {
      icon: { name: 'mdi-umbrella' },
      label: 'Coverage',
      text: 'In the end, people want health insurance so if something major comes up, they can access care and they don\'t end up bankrupt. A plan providing options that reliably do this is a critical measure of whether people will get what they think they paid for when signing up.',
      subs: {
        'clarity': { label: 'Clarity' },
        'assurance': { label: 'Assurance' },
        'relevance': {label: 'Relevance'}
      }
    }
  }
})
</script>

<style lang="scss" scoped>
  .__measure {
    padding: 8px 5px;

    > div {
      &:first-child {
        font-size: var(--text-md);
        font-weight: 600;
        font-family: var(--alt-font);
      }

      &:nth-child(2) {
        font-size: var(--text-xs);
      }
    }
  }
  .__stamp {
    position: absolute;
    top: 0;
    left: 10%;
    transform: translate(0, -10%);
    width: min(200px, 30vw);
    height: min(200px, 30vw);
    z-index: 0;
  }
</style>
