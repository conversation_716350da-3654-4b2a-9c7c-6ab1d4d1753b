<template>
  <div class="row justify-center pd12 relative-position">
    <div class="_cent pw2">
      <div class="row">
        <div class="col-12 col-md-10 col-lg-8 text-ir-deep">
          <div class="__title">The old health plan experience</div>
          <div class="__li">You know the drill: spend a 💰 on premiums - and hope you don't have to use it. If you do use it, your care is dictated by insurance contracts with doctors and facilities.
          </div>
        </div>
      </div>

      <div class="__lax"></div>

    </div>
  </div>
</template>

<script setup>
  import {onMounted} from 'vue';

  onMounted(() => {
    const parallax = document.querySelector('.__lax');
    window.addEventListener('scroll', () => {
      const scrollY = window.scrollY;
      // Adjust 0.3 for a "milder" effect (lower = slower scroll)
      parallax.style.transform = `translateY(${Math.max(0, (scrollY - window.innerHeight * .5)) * 0.2}px)`;
    });
  })
</script>

<style lang="scss" scoped>
  .__lax {
    background-image: url('src/assets/comics/wall_primary.svg');
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    max-width: 1023px;
    background-size: cover;
    background-position: center;
    z-index: -1;
    will-change: transform;
    opacity: .3;
    /* Mild effect */
  }

  .__title {
    font-size: var(--text-xl);
    font-weight: 500;
  }

  .__li {
    padding: 15px 0px;
    font-size: var(--text-sm);
    font-weight: 500;
    font-family: var(--alt-font);
  }

  @media screen and (max-width: 1023px) {
    .__title {
      text-align: center;
    }
    .__lax {
      position: relative;
      height: 100vw;
      background-size: contain;
      background-repeat: no-repeat;
      opacity: 1;
      transform: translateY(-25%);
    }
  }
</style>
