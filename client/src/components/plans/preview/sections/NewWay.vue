<template>
  <div class="pd10">
    <div class="text-center text-xl tw-five q-py-lg pw3 alt-font">Experience better care <span
        class="__typer">quality</span><span
        class="__cursor">|</span></div>
    <div class="row justify-center">
      <div class="_cent __nw">
        <div></div>
        <div class="__blob"></div>
        <div class="__blob2"></div>
        <div class="_fw z2 relative-position">

          <div class="q-py-lg row relative-position mnh400">
            <div class="col-12 col-md-7 pw3 relative-position z2">

              <div class="tw-five text-lg alt-font text-ir-deep">Direct physician contracts for most of your needs</div>
              <div class="text-xs tw-five q-py-md text-ir-deep">Direct Care means that your doctor's primary incentive
                is your health. No deductibles, no 15 minute time-limits, immediate access in-person and virtually.
                <div class="q-pt-md">Even for secondary needs outside of direct care, your doctor is an advocate,
                  connector, and watchman.
                </div>
                <div class="q-pt-md text-primary">Have a doctor you like? 98% of the time you can keep them.</div>
              </div>
            </div>
            <div class="__doc"></div>

          </div>
        </div>
      </div>
    </div>
    <div class="row justify-center">
      <div class="_cent __2w">
        <div class="row relative-position mnh400 text-ir-deep">
          <div class="col-12 col-md-4"></div>
          <div class="col-12 col-md-7 q-py-lg pw3 relative-position z2">
            <div class="tw-five text-lg alt-font">Open-architecture for your major medical coverage</div>
            <div class="text-xs tw-five q-py-md text-ir-deep">"Open" means you have access to virtually any insurance carrier and type.
              Risk pools are complicated - different household and medical situations demand different coverage choices.
              <div class="q-pt-md">Traditional insurance from the largest carriers? Go for it. Medical cost sharing? You
                can do that too. Don't need any? Keep your money.
              </div>
            </div>
          </div>
          <div class="col-12 col-md-1"></div>
          <div class="__pig"></div>
        </div>
      </div>
    </div>
    <div class="row justify-center">
      <div class="_cent __3w">

        <div class="row q-py-md">
          <div class="text-xs tw-five q-py-md text-ir-deep mw800">
            <div class="tw-five text-lg alt-font q-pb-sm">Self-directed will change your care experience</div>
            No more use-it-or-lose-it. If a benefit is good for you - you can choose it. Forfeiting compensation based
            on your choice of health insurance is nonsense.
            <div class="q-pt-md">
              In addition to choice - you also get the benefits of tax-free (where possible), group pricing, and payroll
              deducted.
            </div>
          </div>
        </div>

        <div class="row items-center">
          <div class="col-12 col-md-7">
            <div class="row justify-center">
              <div class="__ch">
                <card-holder :employee-name="employee?.name" :employer-name="employer?.dba || employer?.name"></card-holder>
              </div>
            </div>
          </div>
          <div class="col-12 col-md-5">
            <div class="__gr">
              <div v-for="(opt, i) in options" :key="`opt-${i}`" class="__c">
                {{ opt }}
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>
  </div>
</template>

<script setup>
  import CardHolder from 'assets/comics/CardHolder.vue';

  import {onMounted, ref} from 'vue';

  const props = defineProps({
    employer: { required: false },
    employee: { required: false },
  })

  const options = ['Health Savings', 'Insurance', 'Health Share', 'Child Care', 'Take-Home', 'Retirement', 'Medical Bills', 'Direct Care']

  const typeSpeed = 100;
  const eraseWait = 3000;
  const eraseSpeed = 50;
  const typeWait = 1500;

  const wordIdx = ref(0);
  const words = ['quality', 'price', 'access']

  const eraseText = (l) => {
    const el = document.querySelector('.__typer')
    if(!el) return;
    el.innerHTML = words[wordIdx.value].substring(0, l);
    if (l > 0) setTimeout(() => eraseText(l - 1), eraseSpeed)
    else {
      if (wordIdx.value >= words.length - 1) wordIdx.value = 0;
      else wordIdx.value = wordIdx.value + 1;
      setTimeout(() => typeText(1), typeWait)
    }
  }
  const typeText = (l = 1) => {
    const el = document.querySelector('.__typer')
    if(!el) return
    el.innerHTML = words[wordIdx.value].substring(0, l)
    const len = words[wordIdx.value].length;
    if (l < len) setTimeout(() => typeText(l + 1), typeSpeed)
    else setTimeout(() => eraseText(len - 2), eraseWait)
  }

  onMounted(() => eraseText(0))

</script>

<style lang="scss" scoped>
  $checkers: repeating-linear-gradient(
          90deg,
          white 0px,
          white 2px,
          transparent 2px,
          transparent max(1.5vw, 22px)
  ),
  repeating-linear-gradient(
          180deg,
          white 0px,
          white 2px,
          transparent 2px,
          transparent max(1.5vw, 22px)
  );
  .__nw {
    padding: 8vh 3vw;
    border-radius: max(1vw, 20px);
    position: relative;
    background: var(--q-p0);
    overflow: hidden;

    > div {
      &:first-child {
        background: $checkers;
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        z-index: 0;
      }
    }

    .__blob {
      position: absolute;
      z-index: 1;
      width: 120vw;
      height: 120vw;
      border-radius: 50%;
      animation: roam 20s infinite;
      opacity: 1;
      background: radial-gradient(var(--q-p0), transparent 50%);
      transform: translate(5%, 0);
      bottom: -50vw;
      left: -60vw;
    }

    .__blob1 {
      position: absolute;
      z-index: 1;
      width: 120vw;
      height: 120vw;
      border-radius: 50%;
      animation: roam 20s infinite;
      opacity: 1;
      background: radial-gradient(var(--q-p0), transparent 50%);
      transform: translate(5%, 0);
      top: -50vw;
      right: -50vw;
    }
  }

  .__typer, .__cursor {
    color: var(--q-primary);
  }

  .__cursor {
    animation: blink 1s infinite;
  }

  @keyframes blink {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
  }

  .__doc {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    max-width: 400px;
    height: 100%;
    background: url('src/assets/comics/just_doc_primary.svg');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
  }

  .__2w {
    margin: 20px 0;
    padding: 8vh 3vw;
    border-radius: max(1vw, 20px);
    position: relative;
    background: var(--q-p0);
    overflow: hidden;
    border: solid 1px var(--q-primary);

  }

  .__pig {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    max-width: 400px;
    height: 100%;
    background: url('src/assets/comics/dry_piggy.svg');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
  }

  .__3w {
    background: linear-gradient(180deg, white, var(--q-p0));
    padding: 8vh 3vw;
    border-radius: max(1vw, 20px);
    position: relative;
    overflow: hidden;
  }

  .__ch {
    width: 100%;
    max-width: 400px;
  }

  .__gr {
    display: grid;
    grid-template-columns: 50% 50%;

    > div {
      padding: 15px 5px;
      //border-radius: 10px;
      //background: white;
      display: grid;
      align-items: center;
      justify-items: center;
      font-size: var(--text-sm);
      font-weight: 500;
      //box-shadow: 0 2px 8px var(--q-p2);
      border-bottom: solid 2px var(--q-p2);

      &:nth-child(odd) {
        border-right: solid 2px var(--q-p2);
      }
      &:nth-last-child(1) {
        border-bottom: none;
      }
      &:nth-last-child(2) {
        border-bottom: none;
      }

    }
  }

  @media screen and (max-width: 1023px) {
    .__doc, .__pig {
      position: relative;
      max-width: 100%;
      max-height: 70vw;
      min-height: 300px;
    }
  }
</style>
