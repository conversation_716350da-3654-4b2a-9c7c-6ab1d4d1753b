<template>
  <div class="__plan_structure">
    <div class="__comp">
      <div class="__c __ps_bg text-center tw-five">
        Wages
      </div>
      <div class="__c __ps_bg text-center tw-five">
        Benefits
      </div>
      <div class="__a">
        <div class="__ps_dot"></div>
        <q-icon class="__ic" name="mdi-triangle-down"></q-icon>
      </div>
      <div class="__a">
        <div class="__ps_dot"></div>
        <q-icon class="__ic" name="mdi-triangle-down"></q-icon>
      </div>
    </div>
    <div class="__choice __ps_bg">
      <div class="row items-center justify-center">
        <ai-logo size="20px"></ai-logo>
        <div class="q-ml-sm">Wallet - <span class="text-xxs">Employee Choice</span></div>
      </div>
    </div>


    <div class="__opts">

      <div class="__tw" v-for="i in 4" :key="`ar-${i}`">
        <div class="__ps_dot"></div>
        <q-icon class="__ic" name="mdi-triangle-down"></q-icon>
      </div>


      <div class="__ps_bg">
        <div class="text-center text-md _l1">
          <q-tab-panels keep-alive class="_panel" :model-value="careIdx" animated transition-prev="slide-down"
                        transition-next="slide-down">
            <q-tab-panel v-for="(c, i) in cares" :key="`care-${i}`" class="_panel" :name="i">
              <span v-html="c"></span>
            </q-tab-panel>
          </q-tab-panels>
        </div>
        <div class="__title"><span v-if="$q.screen.gt.xs">Direct </span>Care</div>
      </div>
      <div class="__ps_bg">
        <div class="row justify-around no-wrap">
          <div class="__img">
            <q-tab-panels keep-alive class="_panel" :model-value="mmIdx" animated transition-prev="slide-down"
                          transition-next="slide-down">
              <q-tab-panel class="_panel" v-for="(l, i) in mmlogos" :key="`mm-${i}`" :name="i">
                <q-img class="_fa" :src="l.url"></q-img>
              </q-tab-panel>
            </q-tab-panels>
          </div>
          <div class="__img">
            <q-tab-panels keep-alive class="_panel" :model-value="hsIdx" animated transition-prev="slide-down"
                          transition-next="slide-down">
              <q-tab-panel class="_panel" v-for="(l, i) in hslogos" :key="`hs-${i}`" :name="i">
                <q-img class="_fa" :src="l.url"></q-img>
              </q-tab-panel>
            </q-tab-panels>
          </div>
        </div>
        <div class="__title">Coverage</div>
      </div>
      <div class="__ps_bg">
        <div class="text-center text-md _l1">🏦</div>
        <div class="__title">Savings</div>
      </div>
      <div class="__ps_bg">
        <div class="text-center text-md _l1">🏠</div>
        <div class="__title">Take Home</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import {computed, onMounted, ref} from 'vue';

  const props = defineProps({
    hsLogos: Array,
    mmLogos: Array,
    textColor: String,
    bgColor: String
  })

  const to = 4000;
  const mms = [
    'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fcarrier_logos%2Fkaiser_icon.svg?alt=media&token=f05bef3a-0350-4e7d-b48c-aa4af3a2ee5a',
    'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fcarrier_logos%2Fclearwater_icon.svg?alt=media&token=90c21935-4a71-43c2-9b18-d3b90236e823',
    'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fcarrier_logos%2Fuhc_icon.svg?alt=media&token=f923d3cc-f127-49e6-8496-24a92f73af37'
  ];
  const hss = [
    'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fcarrier_logos%2Fmedishare.svg?alt=media&token=56ed0af4-6df6-49b1-9fb8-8139eada8712',
    'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fcarrier_logos%2Fsedera_icon.png?alt=media&token=dfac53fe-3042-4edf-8192-5d678320009c',
    'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fcarrier_logos%2Fzion_hs.png?alt=media&token=dd13fc15-8a45-4673-a239-2804983f7f7a'
  ]

  const mmlogos = computed(() => [...props.mmLogos || [], ...mms.map(a => {
    return { url: a }
  })].filter(a => !!a).slice(0, 5))
  const hslogos = computed(() => [...props.hsLogos || [], ...hss.map(a => {
    return { url: a }
  })].filter(a => !!a).slice(0, 5))

  const mmIdx = ref(0);
  const hsIdx = ref(0);

  const careIdx = ref(0);
  const cares = ['🩺', '💊', '🩻']

  const cycleMm = () => {
    if (mmlogos.value.length === 1) return mmIdx.value = 0;
    if (mmIdx.value < mmlogos.value.length - 1) mmIdx.value++
    else mmIdx.value = 0;
    setTimeout(() => {
      cycleMm()
    }, to)
  }
  const cycleHs = () => {
    if (hslogos.value.length === 1) return mmIdx.value = 0;
    if (hsIdx.value < hslogos.value.length - 1) hsIdx.value++
    else hsIdx.value = 0;
    setTimeout(() => {
      cycleHs()
    }, to)
  }
  const cycleCare = () => {
    if (careIdx.value < cares.length - 1) careIdx.value++
    else careIdx.value = 0;
    setTimeout(() => {
      cycleCare()
    }, to)
  }

  onMounted(() => {
    cycleMm()
    setTimeout(() => {
      cycleHs()
    }, 1500)
    setTimeout(() => {
      cycleCare()
    }, 1000)
    const setColor = (el, path, color) => {
      if (el) el.style[path] = color
    }
    if (props.bgColor) {
      const blocks = document.querySelectorAll('.__ps_bg')
      for (const a of blocks) {
        setColor(a, 'background', props.bgColor.includes('var(') ? props.bgColor : `var(--q-${props.bgColor})`)
      }
    }
    if (props.textColor) {
      const color = props.textColor.includes('var(') ? props.textColor : `var(--q-${props.textColor})`
      const text = document.querySelectorAll('.__plan_structure, .__ic')
      for (const el of text) {
        if (el) el.style.color = color
      }
      const lines = document.querySelectorAll('.__ps_dot')
      for (const a of lines) {
        setColor(a, 'borderRightColor', color)
      }
    }
  })


</script>

<style lang="scss" scoped>

  $ps-bg: var(--ir-bg);

  .__plan_structure {
    width: 100%;
    color: var(--ir-text);

  }

  ._panel {
    overflow: hidden;
  }

  .__comp {
    width: 100%;
    display: grid;
    grid-template-columns: 65fr 35fr;
    grid-template-rows: auto auto;
    grid-gap: 5px;
    font-size: var(--text-xs);

    .__a {
      display: flex;
      width: 100%;
      justify-content: center;
      padding: 2px;
      position: relative;

      > div {
        height: 25px;
        width: 0;
        border-right: dotted 2px var(--ir-text);
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
      }

    }
  }

  .__ps_bg {
    background: $ps-bg;
  }

  .__c, .__choice {
    width: 100%;
    padding: 12px 8px;
    border-radius: 6px;
    //box-shadow: 0 2px 4px rgba(0,0,0,.1);
  }

  .__choice {
    margin: 10px 0 5px 0;
    font-size: var(--text-xs);
    font-weight: 500;
  }

  .__opts {
    //border-top: dotted 2px white;
    width: 100%;
    display: grid;
    grid-template-columns: auto minmax(auto, 35%) auto auto;
    grid-gap: 5px;
    align-items: center;

    > div {
      height: 100%;
      padding: 4px 8px;
      border-radius: 6px;
      display: grid;
      grid-template-rows: 1fr auto;
      align-items: center;
      grid-gap: 3px;

    }

    .__tw {
      background: none;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      padding: 2px;


      > div {
        height: 25px;
        width: 0;
        border-right: dotted 2px var(--ir-text);
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
      }
    }
  }

  .__img {
    height: 30px;
    width: 30px;
    padding: 0 3px;
  }

  @media screen and (max-width: 600px) {
    .__img {
      height: 20px;
      width: 20px;
    }
  }

  .__title {
    font-size: var(--text-xxs);
    font-weight: 500;
    text-align: center;
    text-wrap: nowrap;
  }

  .__ic {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 0);
    font-size: 8px;
    color: var(--ir-text);
  }

</style>
