<template>
  <q-select
      v-bind="{
        modelValue,
        useInput: true,
        options: pplData.h$.data || [],
        multiple,
        emitValue,
        borderless: true,
        optionValue: '_id',
        ...selectAttrs
      }"
      @update:model-value="emitUp"
      @input-value="search.text = $event"
  >
    <template v-slot:prepend>
      <slot name="prepend">
        <q-icon name="mdi-magnify"></q-icon>
      </slot>
    </template>
    <template v-slot:no-option>
      <div class="text-italic q-pa-sm">Nobody here</div>
    </template>
    <template v-slot:before-options>
      <q-item-label header>Eligible members of {{fullPlan?.value}}</q-item-label>
    </template>
    <template v-slot:option="scope">
      <default-item :model-value="scope.opt" @update:model-value="scope.toggleOption(scope.opt)"></default-item>
    </template>
    <template v-slot:selected-item="scope">
      <default-chip
          @remove="scope.toggleOption(scope.opt)"
          v-bind="{
        removable: true,
        modelValue: scope.opt,
        store: pplsStore,
        ...chipAttrs
      }"
      ></default-chip>
    </template>
  </q-select>
</template>

<script setup>
  import DefaultItem from 'components/common/avatars/DefaultItem.vue';
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';

  import {computed, ref} from 'vue';
  import {planPeople} from 'components/plans/utils/people';
  import {HQuery} from 'src/utils/hQuery';

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    plan: { required: true },
    query: Object,
    modelValue: { required: false },
    selectAttrs: Object,
    chipAttrs: Object,
    multiple:Boolean,
    emitValue: Boolean
  })

  const emitUp = (val) => {
    emit('update:model-value', val);
  };

  const { search, searchQ } = HQuery({});

  const pplQuery = computed(() => {
    return {
      ...props.query,
      ...searchQ.value
    }
  })

  const limit = ref(5);

  const {
    pplsStore,
    fullPlan,
    pplData,
  } = planPeople(computed(() => props.plan), { query: pplQuery, limit })

</script>

<style lang="scss" scoped>

</style>
