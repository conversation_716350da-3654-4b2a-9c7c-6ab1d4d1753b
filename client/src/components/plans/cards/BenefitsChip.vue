<template>
  <q-chip v-bind="{ clickable: true, label: $possiblyPlural('Benefit', activeKeys), ...$attrs}">
    <q-popup-proxy breakpoint="5000">
      <div class="w300 br15 bg-white q-pa-md">
        <q-list separator>
          <q-item-label header>{{activeKeys?.length ? '' : 'No '}}Funding Options</q-item-label>
          <q-item v-for="(k, i) in activeKeys" :key="`k-${i}`">
            <q-item-section>
              <q-item-label class="tw-six">{{getCafeName(k)}}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-popup-proxy>
  </q-chip>
</template>

<script setup>

  import {$possiblyPlural} from 'src/utils/global-methods';
  import {computed} from 'vue';
  import { getCafeName } from 'components/plans/utils';

  const props = defineProps({
    modelValue: {
      required: true, default: () => {
        return {}
      }
    }
  })

  const activeKeys = computed(() => (Object.keys(props.modelValue) || []).filter(a => props.modelValue[a]?.active));
</script>

<style lang="scss" scoped>

</style>
