<template>
  <div class="_fw">
    <div class="row items-center q-pb-sm">
      <q-tabs indicator-color="primary" dense align="left" v-model="tab">
        <q-tab class="font-7-8r" no-caps name="summary" label="Totals"></q-tab>
        <q-tab class="font-7-8r" no-caps name="cafe" label="Plans"></q-tab>
        <q-tab class="font-7-8r" no-caps name="coverage" label="Coverage"></q-tab>
      </q-tabs>
      <q-space></q-space>
      <plan-interval-chip color="transparent" v-model="interval"></plan-interval-chip>
    </div>

    <q-tab-panels class="_panel" animated transition-next="jump-up" transition-prev="jump-down" v-model="tab">
      <q-tab-panel class="_panel" name="summary">
        <div class="__title">Employer Contributions</div>
        <table>
          <tbody>
          <tr>
            <td>Cafeteria</td>
            <td class="alt-font">
              {{ dollarString(employerTotal.cafe * intervals[interval].factor, '$', 2) }}
            </td>
          </tr>
          <tr>
            <td>Coverages</td>
            <td class="alt-font">
              {{ dollarString(employerTotal.coverages * intervals[interval].factor, '$', 2) }}
            </td>

          </tr>
          <tr>
            <td class="tw-six text-accent">Total Employer Contributions</td>
            <td class="tw-six alt-font text-accent">
              {{ dollarString(employerTotal.total * intervals[interval].factor, '$', 2) }}
            </td>
          </tr>
          <tr>
            <td></td>
            <td class="text-accent">This is your "employer cost" {{ interval }}. This will add to your total payroll
              spend
            </td>
          </tr>
          </tbody>
        </table>
        <div class="__title q-mt-sm">Employee Payroll Deductions</div>
        <table>
          <tbody>
          <tr>
            <td>Tax Free</td>
            <td class="alt-font">
              {{ dollarString((contributions.employee.preTax || 0) * intervals[interval].factor, '$', 2) }}
            </td>
          </tr>
          <tr>
            <td>Post Tax</td>
            <td class="alt-font">
              {{ dollarString((contributions.employee.postTax || 0) * intervals[interval].factor, '$', 2) }}
            </td>

          </tr>
          <tr>
            <td>Tax Deferred</td>
            <td class="alt-font">{{
                dollarString((contributions.employee.def || 0) * intervals[interval].factor, '$', 2)
              }}
            </td>

          </tr>
          <tr>
            <td class="tw-six text-ir-deep">Total Employee Elections</td>
            <td class="alt-font tw-six text-ir-deep">{{ dollarString(contributions.employee.total, '$', 2) }}</td>
          </tr>
          <tr>
            <td class="tw-six text-accent">Employer Contributions</td>
            <td class="alt-font tw-six text-accent">
              -{{ dollarString(employerTotal.total * intervals[interval].factor, '$', 2) }}
            </td>
          </tr>
          <tr>
            <td class="tw-six text-p9">Total Employee Deductions</td>
            <td class="alt-font tw-six text-p9">
              {{ dollarString(contributions.needed.total - employerTotal.total, '$', 2) }}
            </td>
          </tr>
          <tr>
            <td></td>
            <td class="text-p9">This amount should be withheld from employee pay and booked as a liability until it is
              spent on the elected benefits.
            </td>
          </tr>
          </tbody>
        </table>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="cafe">
        <div class="__cap">
          Assign funds to a Plan Wallet to control funds for
          employee budgets and cards.
        </div>
        <table>
          <tbody>
          <tr class="tw-six">
            <td>Plan</td>
            <td>Amount</td>
          </tr>
          <tr v-for="(k, i) in Object.keys(fullPlan?.cafe || {})" :key="`cafe-${i}`">
            <td>{{ cafeKeys[k].name }}</td>
            <td class="alt-font">
              {{ dollarString((contributions.byPlan[k] || 0) * intervals[interval].factor, '$', 2) }}
            </td>
          </tr>
          </tbody>
        </table>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="coverage">
        <div class="__cap">
          Assign funds to a Plan Wallet to create isolated account numbers and cards to give to carriers.
        </div>
        <table>
          <tbody>
          <tr class="tw-six">
            <td>Coverage</td>
            <td>Premium
              <q-tooltip>Funds Needed To Cover</q-tooltip>
            </td>
            <td>Acct/Budget
              <q-tooltip>Assigned Plan Wallet Budget</q-tooltip>
            </td>
            <td>EE Budget
              <q-tooltip>Whether to issue sub-budgets for each employee</q-tooltip>
            </td>
          </tr>
          <tr v-for="(cov, i) in coverages || []" :key="`cov-${i}`">
            <td>{{ cov.name }}</td>
            <td class="alt-font">
              {{
                dollarString((byCoverageId[cov._id]?.employer || 0) + (byCoverageId[cov._id]?.employee || 0) * intervals[interval].factor, '$', 2)
              }}
            </td>
            <td>


              <care-account-picker
                  emit-value
                  v-if="!careAccountByCoverageId[cov._id]"
                  dense
                  filled
                  :org="fullPlan?.org"
                  v-model="careAccountByCoverageId[cov._id]"
              ></care-account-picker>
              <budget-chip
                  v-else
                  :care-account="careAccountByCoverageId[cov._id]"
                  :model-value="budgetByCoverageId[cov._id]"
                  @update:model-value="setBudget(cov._id, $event)" picker>
                <template v-slot:top="scope" v-if="budgetByCoverageId[cov._id]">
                  <div class="row">
                    <remove-proxy-btn size="sm" dense flat :icon="undefined" color="accent" name="Budget"
                                  @remove="careAccountByCoverageId[cov._id] = undefined">
                      <template v-slot:default>
                        <span class="q-mr-xs">{{ scope.budget?.name }}</span>
                        <q-icon name="mdi-close" color="red"></q-icon>
                      </template>
                    </remove-proxy-btn>
                  </div>
                </template>
              </budget-chip>


            </td>
            <td style="width: 1%">
              <q-checkbox
                  @update:model-value="toggleEmployeeBudget(cov._id, $event)"
                  v-if="cov.type === 'dc' || cov.covered === 'individual'"
                  :model-value="!!cov.ichra || !!planCoverages[cov._id]?.employeeBudget"
              ></q-checkbox>
            </td>
          </tr>
          </tbody>
        </table>
      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import CareAccountPicker from 'components/care-accounts/lists/CareAccountPicker.vue';
  import BudgetChip from 'components/accounts/issuing/components/budgets/cards/BudgetChip.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import PlanIntervalChip from 'components/plans/utils/PlanIntervalChip.vue';

  import {computed, ref, watch} from 'vue';
  import {$errNotify, dollarString} from 'src/utils/global-methods';
  import {cafeKeys} from 'components/plans/utils';
  import {usePlans} from 'stores/plans';
  import {useBudgets} from 'stores/budgets';
  import {intervals} from '../utils/intervals';

  const planStore = usePlans();

  const budgetStore = useBudgets();
  const props = defineProps({
    plan: { required: true },
    contributions: { required: true },
    coverages: Array,
    planYear: { required: true },
    careAccount: { required: true }
  })

  const fullPlan = computed(() => props.plan);
  const planCoverages = computed(() => fullPlan.value?.coverages || {})

  const employerTotal = computed(() => {
    if (!props.contributions) return 0;
    const { cafe = 0, coverages = 0 } = props.contributions.employer || { cafe: 0, coverages: 0 }
    return { cafe, coverages, total: cafe + coverages }
  })

  const interval = ref('monthly');
  const tab = ref('summary');

  const byCoverageId = computed(() => props.contributions?.byCoverage || {})
  const editing = ref({})
  const setBudget = (covId, budget) => {
    budgetByCoverageId.value[covId] = budget;
    careAccountByCoverageId.value[covId] = budget.careAccount;
    editing.value[covId] = false;
    const patchObj = { $set: { [`coverages.${covId}.budget`]: budget._id } }
    const id = fullPlan.value._id;
    planStore.patchInStore(id, patchObj)
    planStore.patch(id, patchObj);
  }

  //returns an object with key as coverage id and value as budget id
  const budgetIds = computed(() => {
    const res = {};
    const obj = fullPlan.value?.coverages || {}
    for (const k in obj) {
      if (obj[k].budget) res[k] = obj[k].budget;
    }
    return res;
  })

  const careAccountByCoverageId = ref({});
  const budgetByCoverageId = ref({});

  const toggleEmployeeBudget = (covId, val) => {
    const patchObj = { $set: { [`coverages.${covId}.employeeBudget`]: val } }
    const id = fullPlan.value._id;
    planStore.patchInStore(id, {
      coverages: {
        ...fullPlan.value.coverages,
        [covId]: { ...planCoverages.value[covId], employeeBudget: val }
      }
    });
    planStore.patch(id, patchObj, { special_change: [] })
        .catch(err => $errNotify(`Error updating employee budget: ${err.message}`))
  }

  watch(budgetIds, async (nv, ov) => {
    const nk = Object.keys(nv || {})
    if (nk && nk.length !== Object.keys(ov || {}).length) {
      setTimeout(async () => {

        const obj = {};
        const loads = {};
        /**check store for existing value (cache) - if exists set account id in obj*/
        for (let i = 0; i < nk.length; i++) {
          const id = budgetIds.value[nk[i]];
          let got = budgetStore.getFromStore(id).value
          /**otherwise set loads at key:budget id as the coverage id (reversing the object)*/
          if (!got) loads[id] = nk[i]
          else {
            obj[nk[i]] = got.careAccount;
            budgetByCoverageId.value[nk[i]] = got
          }
        }
        const loadIds = Object.keys(loads);
        if (loadIds.length) {
          const bs = await budgetStore.find({
            query: { _id: { $in: loadIds }, $limit: loadIds.length },
            runJoin: { checkMe: true }
          })
          /**iterate through the loaded budgets and set the corresponding coverage id key to the budget account*/
          for (let i = 0; i < bs.data.length; i++) {
            obj[loads[bs.data[i]._id]] = bs.data[i].careAccount
            budgetByCoverageId.value[loads[bs.data[i]._id]] = bs.data[i]
          }
        }
        careAccountByCoverageId.value = obj;
      }, 1000);
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>
  .__title {
    padding: 5px 10px;
    width: 100%;
    font-weight: 600;
    color: var(--ir-mid);
    background: var(--ir-bg2);
  }

  table {
    width: 100%;
    border-collapse: collapse;

    tr {

      td {
        padding: 5px 10px;
        border-bottom: solid .3px #999;
        text-align: left;
        min-width: max(15vw, 120px);

        &:last-child {
          width: 69%;
        }
      }

      &:last-child {
        td {
          border-bottom: none;
        }
      }
    }
  }

  .__cap {
    padding: 10px;
    border-radius: 6px;
    background: #f6f6f6;
    font-size: .85rem;
  }
</style>
