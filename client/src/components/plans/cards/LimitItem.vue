<template>
  <q-item v-bind="{ class: '_fw', ...$attrs }">
    <q-item-section>
      <q-item-label>
        <div class="flex items-center">
          <div class="__title">Single:</div>
          <div class="__body alt-font">{{dollarString(val.single, '$', 0)}}</div>
<!--          <template v-if="isCouple">-->
<!--            <div class="__title">Single +1:</div>-->
<!--            <div class="__body alt-font">{{dollarString(val.family, '$', 0)}}</div>-->
<!--          </template>-->
<!--          <template v-if="isTrio">-->
<!--            <div class="__title">Single +2:</div>-->
<!--            <div class="__body alt-font">{{dollarString(val.single, '$', 0)}}</div>-->
<!--          </template>-->
          <div class="__title">Family:</div>
          <div class="__body alt-font">{{dollarString(val.family, '$', 0)}}</div>

          <div class="q-px-xs">|</div>
          <div class="__title">Type</div>
          <div class="__body">{{dedTypes[val.type || 'annual']}}</div>
        </div>
      </q-item-label>
    </q-item-section>
  </q-item>
</template>

<script setup>
  import {computed} from 'vue';
  import {dollarString} from 'src/utils/global-methods';

  const props = defineProps({
    modelValue: Object
  })

  const val = computed(() => {
    return {
      1: 0,
      2: 0,
      3: 0,
      4: 0,
      ...props.modelValue
    }
  })

  const dedTypes = {
    'event': 'Per Event',
    'annual': 'Per Year'
  }

</script>

<style lang="scss" scoped>
  .__title {
    font-weight: 400;
    font-size: .8rem;
    color: var(--ir-deep);
  }
  .__body {
    font-weight: 600;
    color: var(--ir-text);
    font-size: 1rem;
    padding: 0 10px 0 5px;
  }
</style>
