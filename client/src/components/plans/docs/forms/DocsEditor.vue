<template>
  <div class="_fw">

    <div class="row q-py-sm" v-if="$q.screen.lt.md">
      <q-btn icon="mdi-menu" color="blue-10"  flat @click="menu=!menu"></q-btn>
    </div>
    <div class="row relative-position">
      <div class="col-9 col-lg-2">
        <div :class="`col-12 _fh bg-white __prev ${$q.screen.lt.md && !menu ? '__off' : ''}`">
          <q-item clickable @click="addSection">
            <q-item-section avatar>
              <q-icon name="mdi-plus" color="primary"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label>New Section</q-item-label>
            </q-item-section>
          </q-item>
          <sections-list
              @reorder="reorder"
              @title="setTitle"
              @add="addSub"
              @remove="remove"
              editing
              :model-value="form"
              v-model:active="tab"
          ></sections-list>
        </div>
      </div>
      <div class="col-12 col-lg-10">
        <div class="q-pa-sm row items-center">
          <div class="tw-six font-1-1-4r">{{ tab }}</div>
          <q-btn flat icon="mdi-dots-horizontal">
            <q-menu>
              <div class="w200 bg-white">
                <q-list separator>
                  <q-expansion-item hide-expand-icon>
                    <template v-slot:header>
                      <q-item dense class="_fw _p0">
                        <q-item-section>
                          <q-item-label>Reorder</q-item-label>
                        </q-item-section>
                        <q-item-section side>
                          <q-icon color="blue" name="mdi-arrow-up-down"></q-icon>
                        </q-item-section>
                      </q-item>
                    </template>
                    <q-list separator dense>
                      <q-item v-for="(sec, i) in Object.keys(_get(form, [active.section, 'sections']) || {})" :key="`sub-${i}`" clickable @click="reorder(active.sub, sec, active.section)">
                        <q-item-section>
                          <q-item-label class="tw-six num-font text-ir-grey-7">{{sec}}</q-item-label>
                        </q-item-section>
                      </q-item>
                    </q-list>
                  </q-expansion-item>
                  <q-item clickable>
                    <remove-proxy name="Section" @remove="remove(active.section, active.sub)"></remove-proxy>
                  </q-item>
                </q-list>
              </div>
            </q-menu>
          </q-btn>
        </div>
        <section-form
            @blur="emit('update:model-value', form)"
            :section="tab"
            :custom-values="customValues"
            :custom-args="customArgs"
            v-if="_get(form, [active.section, 'sections', active.sub])"
            :number="tab"
            v-model="form[active.section].sections[active.sub]"
            @update:model-value="emitUp($event, `${active.section}.sections.${active.sub}`)"
            @remove="remove(active.section, active.sub)"
        ></section-form>
        <div v-else class="q-pa-lg">
          <i>Select Section</i>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
  import RemoveProxy from 'components/common/buttons/RemoveProxy.vue';
  import SectionForm from 'components/plans/docs/forms/SectionForm.vue';
  import SectionsList from 'components/plans/docs/cards/SectionsList.vue';
  // import localforage from 'localforage';
  import {computed, onMounted, ref, watch} from 'vue';
  import {_get, _set} from 'symbol-syntax-utils';
  // import { LocalDb } from 'src/utils/db/local-db';

  import { LocalStorage as localforage } from 'symbol-auth-client';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true },
    customValues: { Object },
    customArgs: { type: Object },
    id: { required: true, type: String, default: '__temp' },
    db: { default: 'docs' },
    collection: { default: 'plan-docs' }
  })

  const inc = ref(0);
  // const localdb = new LocalDb(props.db, props.collection, { version: 1 });

  const tab = ref('1.1');
  const menu = ref(false);
  const active = computed(() => {
    const list = (tab.value || '1.1').split('.')
    return {
      section: list[0],
      sub: list[1]
    }
  })

  const formFn = (defs) => {
    return {
      1: {
        sections: {
          1: {}
        }
      },
      ...defs
    }
  }
  const form = ref(formFn());

  const emitUp = async (newVal, path) => {
    const oldVal = path ? _get(form.value, path) : form.value;
    form.value = path ? _set(form.value, path, newVal) : newVal;

    // const localDoc = await localdb.getItem(form.value._id || props.id);
    const localDoc = await localforage.getItem(form.value._id || props.id);

    const fj = { ...localDoc?._fastjoin, ...form.value._fastjoin, editIndex: 0, edits: [{ path, oldVal, newVal }, ...localDoc?._fastjoin?.edits || []].slice(50) }
    localforage.setItem(form.value._id || props.id, { ...form.value, _fastjoin: fj });
    // localdb.putItem(form.value._id || props.id, { ...form.value, _fastjoin: fj })
  }

  const setTitle = (key, value) => {
    form.value[key].title = value;
    emit('update:model-value', form.value);
  }

  const addSection = () => {
    const next = (Object.keys(form.value || {}).length || 0) + 1;
    const time = new Date().getTime();
    const nextObj = {
      ...form.value,
      [next]: {
        title: 'New Section',
        key: `m${time}`,
        sections: {
          1: {
            title: 'First Section',
            key: `s${time}`,
            body: '## Section details go here'
          }
        }
      }
    }
    emitUp(nextObj)
  }
  // if > key && < toKey key move down
  // if < toKey move up
  const reorder = (k1, k2, parent, remove) => {
    const key = Number(k1);
    const toKey = Number(k2);
    const newForm = {};
    const obj = parent ? form.value[parent].sections : form.value;
    for(const kk in obj){
      const k = Number(kk);
      if(k < key && k >= toKey){
        //move k up
        newForm[k+1] = obj[k];
      } else if(k > key && k <= toKey){
        //move k down
        newForm[k-1] = obj[k];
      } else if(k === key) {
        newForm[toKey] = obj[k];
      } else newForm[k] = obj[k];
    }
    if(!!parent || parent === 0) emitUp({ ...form.value[parent], sections: newForm }, parent);
    else emitUp(newForm);
  }

  const addSub = (sec) => {
    const next = Object.keys(form.value[sec].sections || {}).length + 1;
    form.value[sec].sections[next] = {
      title: '',
      body: '',
      key: `s${new Date().getTime()}`
    }
  }

  const remove = (k, sub) => {
    const obj = Object.assign({}, form.value);
    if(sub) {
      delete obj[k].sections[sub];
      for(const subK in obj[k].sections){
        const numSub = Number(sub);
        if(Number(subK) > numSub) {
          obj[k].sections[numSub - 1] = obj[k].sections[numSub];
          if(!obj[k].sections[numSub + 1]) delete obj[k].sections[numSub];
        }
      }
      emitUp(obj[k].sections, `${k}.sections`)
    } else {
      delete obj[k];
      for(const key in obj){
        const numK = Number(k);
        if(Number(key) > numK) {
          obj[numK - 1] = obj[numK];
          if(!obj[numK + 1]) delete obj[numK];
        }
      }
      emitUp(obj)
    }
  }

  const doing = ref(false);
  const undo = async () => {
    if(!doing.value) {
      doing.value = true;
      const localDoc = await localforage.getItem(form.value._id || props.id)
      // const localDoc = await localdb.getItem(form.value._id || props.id)
      const fj = { ...form.value._fastjoin, ...localDoc?._fastjoin };
      const edits = fj?.edits || [];
      if (edits.length) {
        const index = localDoc?._fastjoin?.editIndex || 0;
        const { oldVal, path } = edits[index] || {};
        if(oldVal){
          if(path) form.value = _set(form.value, path, oldVal);
          else form.value = oldVal;
          emit('update:model-value', form.value);
          fj.editIndex = index + 1;
          localforage.setItem(form.value._id || props.id, { ...form.value, _fastjoin: fj})
          // localdb.putItem(form.value._id || props.id, { ...form.value, _fastjoin: fj})
        }
      }
      doing.value = false;
    }
  }

  const redo = async () => {
    if(!doing.value) {
      doing.value = true;
      // const localDoc = await localdb.getItem(form.value._id || props.id)
      const localDoc = await localforage.getItem(form.value._id || props.id)
      const fj = { ...form.value._fastjoin, ...localDoc?._fastjoin };
      const edits = fj?.edits || [];
      if (edits.length && fj?.editIndex >= 0) {
        const { newVal, path } = edits[fj.editIndex]
        if(newVal){
          if(path) form.value = _set(form.value, path, newVal);
          else form.value = newVal;
          emit('update:model-value', form.value);
          fj.editIndex--;
          // localdb.putItem(form.value._id || props.id, { ...form.value, _fastjoin: fj})
          localforage.setItem(form.value._id || props.id, { ...form.value, _fastjoin: fj})
        }
      }
      doing.value = false;
    }
  }

  const keyDown = (e) => {
    if(e.key === 'z'){
      if(e.metaKey || e.ctrlKey){
        if(e.shiftKey) redo();
        else undo();
      }
    }
  }

  onMounted(async () => {
    window.addEventListener('keydown', keyDown);
  })

  watch(() => props.modelValue, (nv) => {
    if(nv) form.value = formFn(nv);
  }, { immediate: true })

</script>

<style lang="scss" scoped>
  .__prev {
    position: relative;
    border-right: solid 4px #e0e0e0;
    z-index: 20;
    background: var(--q-grey-1);
  }

  @media screen and (max-width: 1023.99px) {
    .__prev {
      position: absolute;
      top: 0;
      left: 0;
      width: 400px;
      max-width: 100%;
      height: 100%;
      overflow-x: scroll;
      transition: width .3s;
    }
    .__off {
      width: 0;
      overflow: hidden;
    }
  }

  .__tab {
    width: 100%;
    max-width: 90vw;
    overflow-x: scroll;
  }

  .__md_panel {
    width: 100%;
    overflow-x: scroll;
    padding: 0 !important;
  }
</style>
