<template>
  <div class="_fw">
    <div class="q-pa-sm">
      <q-input input-class="tw-six" label="Section Title" v-model="form.title" @update:model-value="autoSave"></q-input>
    </div>
    <div class="q-pa-sm" v-if="customValues">
      <q-btn no-caps flat>
        <span class="tw-six text-blue">${dynamic values}</span>
        <q-icon color="black" name="mdi-menu-down"></q-icon>
        <q-menu>
          <div class="w200 bg-white">
            <q-list separator>
              <q-item-label header>Values from Plan Settings</q-item-label>
              <q-item v-for="(val, i) in Object.keys(customValues || {})" :key="`val-${i}`" clickable
                      @click="form.body += ('${' + val + '}')">
                <q-item-section>
                  <q-item-label>{{ customValues[val].label }}</q-item-label>
                  <q-item-label caption>{{val}}</q-item-label>
                  <q-item-label caption>{{ customValues[val].description }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-menu>
      </q-btn>
    </div>
    <div class="_fw">
      <q-splitter :horizontal="$q.screen.lt.md" v-model="splitter">
        <template v-slot:before>
          <div class="_fw">
            <q-tab-panels v-model="editor" animated class="_panel bg-ir-grey-2">
              <q-tab-panel class="__md_panel _panel" name="markdown">
                <md-editor
                    :preview="false"
                    language="en-US"
                    v-model="form.body"
                    @update:model-value="emitUp"
                    :toolbars-exclude="toolbarsExclude"
                    @focus="editorFocus = true"
                    @blur="$emit('blur')"
                ></md-editor>
              </q-tab-panel>
              <q-tab-panel class="_panel" name="rich">
                <full-input
                    @blur="$emit('blur')"
                    min-height="600px"
                    content-class="q-px-md bg-white"
                    :model-value="html"
                    @focus="editorFocus = true"
                    @update:model-value="setHtml"
                ></full-input>
              </q-tab-panel>
            </q-tab-panels>
            <div class="row items-center q-pa-sm">
              <q-btn-group push>
                <q-btn class="tw-six" color="p7" :outline="editor !== 'markdown'" size="sm" label="Markdown"
                       @click="editor = 'markdown'"></q-btn>
                <q-btn class="tw-six" color="p7" :outline="editor !== 'rich'" size="sm" label="Rich Text"
                       @click="setRich"></q-btn>
              </q-btn-group>
            </div>
          </div>
        </template>

        <template v-slot:separator>
          <q-avatar color="primary" text-color="white" size="30px" icon="drag_indicator"/>
        </template>

        <template v-slot:after>
          <div class="_fw __preview">
            <q-chip square color="ir-grey-5" dark class="tw-six">Preview</q-chip>
            <md-preview
                :model-value="useBody"
            ></md-preview>
          </div>
        </template>
      </q-splitter>
    </div>
  </div>
</template>

<script setup>
  import FullInput from 'components/common/input/FullInput.vue';

  import {MdEditor, MdPreview} from 'md-editor-v3';
  import 'md-editor-v3/lib/style.css';
  import {computed, nextTick, ref, watch} from 'vue';
  import {Screen} from 'quasar';
  import {NodeHtmlMarkdown} from 'node-html-markdown';
  import showdown from 'showdown';

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    modelValue: Object,
    customValues: Object,
    customArgs: Object,
    section: String
  })

  const editorFocus = ref(false);
  const editor = ref('markdown');
  const splitter = ref(50);

  const toolbarsExclude = computed(() => {
    const all = ['save', 'github', 'fullscreen', 'image', 'htmlPreview'];
    if (Screen.lt.md) return [...all, 'sup', 'code', 'codeRow', 'katex', 'task', 'quote', 'strikeThrough', 'sub', 'table', 'mermaid', 'title', 'underline', 'pageFullScreen']
    else return all;
  })

  const htmlDirty = ref(false);

  const formFn = (defs) => {
    const time = String(new Date().getTime());
    return {
      body: '',
      key: time,
      ...defs
    }
  }
  const form = ref(formFn())

  const useBody = ref('');
  const to = ref();
  const filterBody = (val) => {
    useBody.value = val;
    const keys = Object.keys(props.customValues || {});
    if(keys.length) {
      if (to.value) clearTimeout(to.value);
      to.value = setTimeout(() => {
        const replacePlaceholders = (text, cvKeys, args) => {
          return text.replace(/\${(.*?)}/g, (match, key) => {
            const replacement = cvKeys.find(item => item === key);
            if (replacement) return props.customValues[key].value(args)
            return replacement ? replacement.value : match; // keep original if no replacement is provided
          });
        }
        useBody.value = replacePlaceholders(useBody.value, keys, props.customArgs || {})

      }, 2000)
    }
  }

  let timer;
  const autoSave = () => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      if (htmlDirty.value) {
        filterBody(form.value.body)
        form.value.body = NodeHtmlMarkdown.translate(html.value)
        htmlDirty.value = false;
      }
      emit('update:model-value', form.value)
    }, 1000)
  }

  const html = ref('');
  const setHtml = (val) => {
    htmlDirty.value = true;
    html.value = val;
    autoSave()
  }

  const setRich = () => {
    editor.value = 'rich'
    const converter = new showdown.Converter()
    html.value = converter.makeHtml(form.value.body);
    filterBody(form.value.body);
  }

  const emitUp = () => {
    if(editorFocus.value) {
      filterBody(form.value.body);
      emit('update:model-value', form.value)
    }
  }

  watch(() => props.modelValue, (nv, ov) => {
    if(nv && nv !== ov){
      nextTick(() => {
        if(props.modelValue) form.value = formFn(props.modelValue);
        if(form.value.body) filterBody(form.value.body);
        if(form.value.body && editor.value === 'rich') {
          const converter = new showdown.Converter()
          html.value = converter.makeHtml(form.value.body);
        }
      })
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>
  .__preview {
    max-height: 500px;
    overflow-y: scroll;
    border-bottom: solid .1px #999;
  }
</style>
