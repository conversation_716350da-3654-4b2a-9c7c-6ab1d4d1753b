<template>
  <common-dialog :model-value="modelValue" @update:model-value="emitUp" setting="full">
    <div class="_fa">
      <div class="row justify-center">
        <div class="_cent">
          <div class="q-pa-lg" v-if="!isSet">
            <q-spinner color="primary" size="50px"></q-spinner>
          </div>
          <template v-else>
            <div class="_fw text-center q-py-md font-1r">
              <div>Last Indexed: {{ $ago(lastUpdate, 'MM/DD/YYYY', 'Never') }}</div>
              <div class="row justify-center q-pt-sm">
                <q-btn no-caps push color="primary" @click="update">
                  <span>Looks Good. Update Index Now</span>
                  <q-icon v-if="!updating" name="mdi-refresh"></q-icon>
                  <q-spinner v-else color="white"></q-spinner>
                </q-btn>
              </div>
            </div>
            <div class="_fw" id="PlanMeat">
              <div style="padding: 20px 0;">
                <print-display :plan-docs="planDocs"></print-display>
              </div>

              <schedule-a :plan="fullPlan"></schedule-a>
              <schedule-b :plan="fullPlan"></schedule-b>
              <schedule-c :plan="fullPlan" :by-org="byOrg"></schedule-c>
              <schedule-d :plan="fullPlan"></schedule-d>
            </div>
          </template>
        </div>
      </div>
    </div>

  </common-dialog>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import PrintDisplay from 'components/plans/docs/utils/PrintDisplay.vue';
  import ScheduleD from 'components/plans/docs/schedules/ScheduleD.vue';
  import ScheduleC from 'components/plans/docs/schedules/ScheduleC.vue';
  import ScheduleA from 'components/plans/docs/schedules/ScheduleA.vue';
  import ScheduleB from 'components/plans/docs/schedules/ScheduleB.vue';

  import {docDisplay} from 'components/plans/docs/utils/display';
  import {computed, ref} from 'vue';
  import {$ago} from 'src/utils/global-methods';
  import {NodeHtmlMarkdown} from 'node-html-markdown';


  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true },
    planId: { required: true }
  })

  const { byOrg, isSet, fullPlan, planDocs, store } = docDisplay(computed(() => props.planId))

  const updated = ref();
  const lastUpdate = computed(() => updated.value || fullPlan.value.vectorStoreIds?.plan_docs?.updatedAt)
  const emitUp = (val) => {
    emit('update:model-value', val);
  }
  const updating = ref(false);
  const update = async () => {
    const html = document.getElementById('PlanMeat').innerHTML;
    console.log('html', html);
    const docs_as_md = NodeHtmlMarkdown.translate(html);
    console.log('docs_as_md', docs_as_md);
    updating.value = true;

    setTimeout(async () => {
      const res = await store.patch(planDocs.value[Object.keys(planDocs.value)[0]]._id, { updatedAt: new Date() }, { runJoin: { docs_as_md } })
          .catch(err => {
            console.error(`Error updating vector store: ${err.message}`)
            throw new Error(`Error updating: ${err.message}`)
          })
      updating.value = false;
      if (res._id) {
        updated.value = new Date();
        emit('update:model-value', false);
      }
    }, 1000);
  }
</script>

<style lang="scss" scoped>

</style>
