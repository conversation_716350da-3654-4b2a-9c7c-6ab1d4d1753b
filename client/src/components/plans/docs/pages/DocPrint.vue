<template>
  <q-page>
    <template v-if="!printView">

      <div class="_fw row justify-center">
        <div class="_sent q-py-lg pw2 bg-white">

          <q-tabs align="left" :model-value="tab" @update:model-value="setTab">
            <q-tab label="Plan Document" no-caps name="doc"></q-tab>
            <q-tab label="Summary Plan Description" no-caps name="spd"></q-tab>
          </q-tabs>

          <div class="q-py-md row justify-end">
            <q-btn class="_s_btn" @click="print" no-caps>
              <span class="q-mr-sm">Print</span>
              <q-spinner v-if="printing" color="white"></q-spinner>
              <q-icon v-else name="mdi-file-pdf-box"></q-icon>
            </q-btn>
          </div>

          <div class="q-pa-lg" v-if="!isSet">
            <q-spinner color="primary" size="50px"></q-spinner>
          </div>
        </div>
      </div>
      <q-tab-panels v-if="isSet" style="padding: 0;" v-model="tab" animated>
        <q-tab-panel name="doc" style="padding: 0;">


          <div style="width: 100%" id="PlanDoc">
            <div class="row justify-center">
              <div class="_sent pw2 pd6">
                <plan-title :by-org="byOrg" :title="fullPlan?.name"></plan-title>
                <div class="_fw" id="PlanMeat">
                  <div style="padding: 20px 0;">
                    <print-display :plan-docs="planDocs"></print-display>
                  </div>

                  <schedule-a :plan="fullPlan"></schedule-a>
                  <schedule-b :plan="fullPlan"></schedule-b>
                  <schedule-c :plan="fullPlan" :by-org="byOrg"></schedule-c>
                  <schedule-d :plan="fullPlan"></schedule-d>
                </div>
              </div>

            </div>
          </div>
        </q-tab-panel>
        <q-tab-panel name="spd" style="padding: 0;">

          <div style="width: 100%;" id="SPD">
            <div class="row justify-center">
              <div class="_sent pw2 pd6">
                <plan-title :by-org="byOrg" :title="fullPlan?.name" subtitle="Summary Plan Description"></plan-title>

                <div style="width: 100%;">

                  <print-display :plan-docs="{[spd._id]: spd}"></print-display>

                  <schedule-a :plan="fullPlan"></schedule-a>
                  <schedule-b :plan="fullPlan"></schedule-b>
                  <schedule-c :plan="fullPlan" :by-org="byOrg"></schedule-c>
                  <schedule-d :plan="fullPlan"></schedule-d>
                </div>
              </div>
            </div>
          </div>
        </q-tab-panel>
      </q-tab-panels>
    </template>

    <template v-else>
      <div class="_fa">
        <div class="row q-px-md q-py-sm">
          <q-btn dense flat icon="mdi-close" @click="printView = false"></q-btn>
        </div>
        <template v-if="!tooBig">
        <file-preview :model-value="{ url: pdfUrl, info: { name: docNames[tab], type: 'application/pdf' } }"></file-preview>
        </template>
        <template v-else>
          <div class="q-pa-lg font-1r text-italic">File too large to preview - it was opened or downloaded automatically</div>
        </template>
      </div>
    </template>

  </q-page>
</template>

<script setup>
  import PlanTitle from 'components/plans/docs/utils/PlanTitle.vue';
  import PrintDisplay from 'components/plans/docs/utils/PrintDisplay.vue';
  import ScheduleA from 'components/plans/docs/schedules/ScheduleA.vue';
  import ScheduleB from 'components/plans/docs/schedules/ScheduleB.vue';
  import ScheduleC from 'components/plans/docs/schedules/ScheduleC.vue';
  import ScheduleD from 'components/plans/docs/schedules/ScheduleD.vue';
  import FilePreview from 'components/common/uploads/pages/FilePreview.vue';

  import {docDisplay} from 'components/plans/docs/utils/display';
  import {computed, onMounted, ref} from 'vue';
  import {useRoute, useRouter} from 'vue-router';

  import {handlePrint} from 'components/plans/docs/utils/print';

  const route = useRoute();
  const router = useRouter();

  const props = defineProps({
    planId: { required: false }
  })

  const { byOrg, isSet, spd, fullPlan, planDocs } = docDisplay(computed(() => props.planId))

  const tab = ref('doc');


  const setTab = (val) => {
    tab.value = val;
    router.push({ ...route, params: { ...route.params, tab: val } })
  }

  const printIds = {
    'doc': 'PlanDoc',
    'spd': 'SPD'
  };

  const docNames = computed(() => {
    return {
      'doc': fullPlan.value?.name || 'Untitled',
      'spd': spd.value?.name || 'Summary Plan Description'
    }
  })

  const { print, printing, pdfUrl, printView, tooBig } = handlePrint(computed(() => printIds[tab.value]))

  onMounted(() => {
    if (route.params.tab) tab.value = route.params.tab;
  })

</script>

<style lang="scss" scoped>


</style>

