<template>
  <div style="width: 100%">
    <div style="padding-top: 10px; font-size: 2rem; font-weight: 600;">{{ title }}</div>
    <div style="padding-bottom: 10px; font-size: 2rem; font-weight: 600; color: #999;">
      <span v-if="subtitle">{{subtitle}}</span>
    </div>

    <div style="padding-bottom: 20px;">
      <div style="padding-bottom: 10px; width: 100%;">
        <div style="color: #999; font-size: 1rem;">Provided for:</div>
      </div>
      <div style="width: 100%; font-size: 1rem;">
        <div v-for="(orgId, i) in Object.keys(byOrg)" :key="`grp-${i}`" style="font-size: 1rem;">
          <div style="font-weight: 600">{{ byOrg[orgId].org?.name }}</div>
          <div style="font-size: .85rem; width: 100%;">
                  <span v-for="(grp, idx) in byOrg[orgId].groups || []"
                        :key="`grp-${i}-${idx}`">{{ idx === 0 ? '' : ', ' }}{{ grp.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

  const props = defineProps({
    title: String,
    subtitle: String,
    byOrg: Object
  })
</script>

<style lang="scss" scoped>

</style>
