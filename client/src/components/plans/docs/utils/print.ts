import {fakeId} from 'src/utils/global-methods';
import {axiosFeathers, restCore} from 'components/common/uploads/services/utils';
import {AnyRef} from 'src/utils/types';
import {ref} from 'vue';

export const handlePrint = (id:AnyRef<string>) => {

    const printView = ref(false);
    const printing = ref(false);
    const pdfUrl = ref('')
    const lastPrint = ref('')

    const getPrintableHtml = () => {
        const el = document.getElementById(id.value);
        const html = el.outerHTML;

        // Manually include any style rules you need
        const styleSheets = Array.from(document.styleSheets);
        const styles = styleSheets
            .map(sheet => {
                try {
                    return Array.from(sheet.cssRules || [])
                        .map(rule => rule.cssText)
                        .join('\n');
                } catch {
                    return ''; // Cross-origin stylesheets are skipped
                }
            })
            .join('\n');

        return `
    <html>
      <head>
        <meta charset="utf-8" />
        <style>${styles}</style>
      </head>
      <body style="margin: 0; padding: 40px; font-family: Arial, sans-serif;">
        ${html}
      </body>
    </html>
  `;
    }

    const tooBig = ref(false);
    const print = async (download?:boolean) => {
        if(pdfUrl.value && lastPrint.value === id.value) return printView.value = true;
        printing.value = true;
        lastPrint.value = id.value;
        try {
            const html = getPrintableHtml()
            const blob = new Blob([html], { type: 'text/html' });
            const file = new File([blob], 'print-content.html', { type: 'text/html' });

            const formData = new FormData();
            formData.append('file', file);
            formData.set('plan', fakeId);
            formData.set('storage', 'print');

            const query = {
                storage: 'print'
            }
            const printedPdf:any = await axiosFeathers().post('/uploads', formData, {
                params: {
                    ...query,
                    core: restCore(),
                    runJoin: { printPdf: true, add_files: true }
                }
            })
                .catch(err => console.error(`Error printing pdf: ${err.message}`))

            const pdfBuffer = printedPdf.data?.buffer;
            if (pdfBuffer) {
                const byteArray = Uint8Array.from(Object.values(pdfBuffer));
                const sizeInMB = byteArray.byteLength / (1024 * 1024);

                const blob = new Blob([byteArray.buffer], { type: 'application/pdf' });
                const url = URL.createObjectURL(blob);
                console.log('checking size', sizeInMB, printedPdf.data);
                if(!download && sizeInMB < 8){
                    pdfUrl.value = url;
                    printView.value = true;
                } else {
                    tooBig.value = true;
                    window.open(url, '_blank');
                }
            }

        } catch (e) {
            console.error(`Error printing pdf: ${e.message}`)
        }
        printing.value = false;

    }


    return { print, printing, pdfUrl, printView, lastPrint, tooBig };
}
