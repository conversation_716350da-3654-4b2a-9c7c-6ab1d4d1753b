import {computed, ComputedRef, Ref, ref} from 'vue';
import {dollarString} from 'src/utils/global-methods.js';
import {formatDate, subtractFromDate, firstDateOfNextMonth} from 'src/utils/date-utils.js';
export declare type CafeKey = 'hsa'|'pop'|'fsa'|'dcp'|'cash';
export const cafeKeys = {
    pop: {
        name: 'Premium Only Plan',
        shortName: 'POP',
        smallName: 'Premium Only',
        ad: 'This is where you use payroll to pay premiums tax-free.',
        description: 'The only legal vehicle for employees to pay health plan premiums on a tax free basis',
        class: '125',
        docPath: 'cafe.pop.doc',
        subClass: 'POP'
    },
    hsa: {
        name: 'Health Savings Account',
        shortName: 'HSA',
        smallName: 'Health Savings',
        ad: 'Put money aside for healthcare costs tax-free (including payroll tax when done through an employer plan). You direct your own HSA and funds roll over indefinitely.',
        description: 'Participant-directed pre-tax account for out of pocket medical expenses. HSA\'s roll over indefinitely. Employer sponsored HSA is payroll tax free.',
        docPath: 'cafe.hsa.doc',
        class: '125',
        subClass: 'HSA'
    },
    fsa: {
        name: 'Flex Spending',
        shortName: 'FSA',
        smallName: 'Flex Spending',
        description: 'Pretax employer directed out of pocket medical expense account. Limited rollover year-to-year.',
        ad: 'Cover un-reimbursed medical expenses tax-free. Use an HSA instead unless you are not eligible for an HSA (due to not having a high deductible plan).',
        class: '125',
        docPath: 'cafe.fsa.doc',
        subClass: 'FSA'
    },
    dcp: {
        name: 'Dependent Care Plan',
        smallName: 'Dependent Care',
        shortName: 'DCAP',
        ad: 'Pay for dependent care expenses through payroll tax-free',
        description: 'Pretax account for child-care or dependent-care expenses.',
        class: '125',
        docPath: 'cafe.dcp.doc',
        subClass: 'DCAP'
    },
    // ebp: {
    //     name: 'Excepted Benefits',
    //     shortName: '',
    //     class: '125',
    //     subClass: 'EBP'
    // },
    def: {
        name: 'Retirement',
        shortName: '401(k)',
        smallName: 'Deferred',
        ad: 'Any cafeteria funds not used on health or dependent care costs can be used toward retirement benefits.',
        description: 'Retirement benefits are not tax-free, but are tax deferred. Payroll taxes are due, income taxes are not.',
        class: '125',
        docPath: 'cafe.def.doc',
        subClass: 'def'
    },
    cash: {
        name: 'Taxable Benefits',
        shortName: 'Taxable',
        smallName: 'Taxable',
        ad: 'Any cafeteria funds not used on health or dependent care costs can be used toward taxable benefits - including just taking cash disbursements (normal payroll in-effect).',
        description: 'Contributions that are not assigned to a pre-tax benefit default to normal payroll - which is considered the "cash" benefit. However, this could also include taxable voluntary benefits.',
        class: '125',
        docPath: 'cafe.cash.doc',
        subClass: 'cash'
    }
}
export const hraKeys = {
    'ICHRA': {
        name: 'Individual Coverage HRA',
        shortName: 'ICHRA',
        description: 'Group plan reimbursement of individual health insurance premiums, direct care arrangements, and some excepted out of pocket costs.',
        covers: 'Insurance Premiums',
        class: '105',
        subClass: 'ICHRA',
        color: ''
    },
    'EBHRA': {
        name: 'Excepted Benefit HRA',
        shortName: 'EBHRA',
        description: 'Group plan reimbursement of out of pocket medical expenses not covered by other plans.',
        covers: 'Excepted Medical Expense',
        class: '105',
        subClass: 'EBHRA'
    },
    'GCHRA': {
        name: 'General Coverage HRA',
        shortName: 'GCHRA',
        description: 'Group plan reimbursement of participant medical expenses - a self funded form of group health insurance',
        covers: 'Major Medical Expense',
        class: '105',
        subClass: 'GCHRA'
    }
}
export const subObj = {
    'general': {
        name: 'Main Plan Doc'
    },
    'CASH': {
        name: 'Taxable 125 Benefits'
    },
    'POP': {
        name: 'Premium Only 125'
    },
    'HSA': {
        name: 'Section 125 HSA'
    },
    'FSA': {
        name: 'Section 125 FSA'
    },
    'DCAP': {
        name: 'Dependent Care Plan'
    },
    ...hraKeys
}

const subLists = {
    'core': ['general'],
    '125': ['CASH', 'POP', 'HSA', 'FSA', 'DCAP'],
    '105': Object.keys(hraKeys),
    'misc': ['spd']
}

export const planClasses = {
    'core': {
        name: 'Core Plan Doc',
        color: 'teal-8',
        sub: subLists['core']
    },
    '125': {
        name: 'Section 125 Plan',
        color: 'blue',
        sub: subLists['125']
    },
    '105': {
        name: 'Section 105 Plan',
        color: 'purple-9',
        sub: subLists['105']
    },
    'misc': {
        name: 'Misc Docs',
        color: 'orange-9',
        sub: subLists['misc']
    },
    'spd': {
        name: 'Summary Plan Description',
        color: 'green-6',
        sub: []
    }
}

export declare type Plan = { [key:string]: any }
type Def = string|number
type Args = { plan:Plan }

export const customValues:Ref<{[key:string]: { label: string, value: (args:Args, def?:Def) => Def }}> = ref({
    'planName': {
        label: 'Plan Name',
        value: ({plan}:Args, def = 'Group Health Plan'):Def =>  plan?.name || def
    },
    'sponsorName': {
        label: 'Plan Sponsor Name',
        value: ({plan}:Args, def = 'Plan Sponsor'):Def => plan?.info?.sponsorName || def
    },
    'employerContribution': {
        label: 'Employer Contribution',
        value: ({plan}:Args, def = 'N/A'):Def => {
            const { amount, type, match } = plan?.employerContribution || { amount: 0 }
            if(!amount) return def
            else {
               const fixes = type === 'percent' ? ['', '%', 1, ' of wages'] : ['$', '', amount > 50 ? 0 : 1, ''];
               return dollarString(amount, fixes[0] as string, fixes[2] as number) + fixes[1] + fixes[3] + match ? ' as a match of participant contributions' : ''
            }
        }
    },
    'planYearStart': {
        label: 'Plan Year Start',
        value: ({plan}:Args, def = 'January 1', format = 'MMMM DD'):Def => formatDate(plan?.planYearStart || firstDateOfNextMonth(), format) || def
    },
    'planYearEnd': {
        label: 'Plan Year End',
        value: ({plan}:Args, def = 'December 31', format = 'MMMM DD'):Def => formatDate(subtractFromDate(plan?.planYearStart || formatDate(firstDateOfNextMonth(), 'MM/DD/YYYY'), { 'days': 1 }), format) || def
    },
    'exclusionPeriod': {
        label: 'Initial Ineligible Period',
        value: ({plan}:Args, def = 90):Def => plan?.eligibility?.term || def
    },
    'fullTimeHours': {
        label: 'Hours Per Week Required',
        value: ({plan}:Args, def = 30):Def => plan?.eligibility?.hours || def
    },
    'limits': {
        label: 'Legal Contribution Limits',
        value: ({plan}:Args, def = 0):Def => ''
    },
    'benefitList': {
        label: 'List of plan benefits',
        value: ({plan}:Args, def = ''):Def => {
            let str = ''
            for(const k in cafeKeys){
                if((plan?.cafe || { [k]: { active: false }})[k]?.active) {
                    const val = cafeKeys[k as keyof typeof cafeKeys];
                    str += ` - ${val.name}${val.shortName && val.shortName !== val.name ? '(' + val.shortName + ')' : ''}`
                }
            }
            for (const k in hraKeys){
                if((plan?.hra || { [k]: { active: false }})[k]?.active) {
                    const val = hraKeys[k as keyof typeof hraKeys];
                    str += ` - ${val.name}${val.shortName ? '(' + val.shortName + ')' : ''}`
                }
            }
            return `${str || def}`
        }

    },
    'benefitSummary': {
        label: 'List of plan benefits with brief descriptions',
        value: (plan:Plan, def = ''):Def => {
            let str = ''
            for(const k in cafeKeys){
                if((plan?.cafe || { [k]: { active: false }})[k]?.active) {
                    const val = cafeKeys[k as keyof typeof cafeKeys];
                    str += ` - *${val.name}${val.shortName ? '(' + val.shortName + ')' : ''}:* ${val.description}`
                }
            }
            for (const k in hraKeys){
                if((plan?.hra || { [k]: { active: false }})[k]?.active) {
                    const val = hraKeys[k as keyof typeof hraKeys];
                    str += ` - *${val.name}${val.shortName ? '(' + val.shortName + ')' : ''}:* ${val.description}`
                }
            }
            return `${str || def}`
        }
    },
    'planSponsorInfo': {
        label: 'Contact info for the plan sponsor',
        value: (plan:Plan, def = 'See benefits site for details'):Def => {
            const { sponsor } = plan?.info || { sponsor: { name: def, phone: '', email: '', address: '' } }
            let str = sponsor?.name || '';
            for(const val of ['phone', 'email', 'address']){
                const v = (sponsor || {})[val] || ''
                if(v) str += `; ${v}`
            }
            return str || def;
        }
    },
    'EIN': {
        label: 'Plan Sponsor EIN',
        value: (plan:Plan, def = 'See benefits site for details'):Def => {
            const { sponsor: { ein } } = plan?.info || { sponsor: { ein:  def } }
            return ein || def
        }
    },
    'planAdministratorInfo': {
        label: 'Plan Administrator contact info',
        value: (plan:Plan, def = 'See benefits site for details'):Def => {
            const { planAdmin, sponsor } = plan?.info || { planAdmin: { name: undefined }, sponsor: { name: def } }
            let str = planAdmin?.name || '';
            for(const val of ['phone', 'email', 'address']){
                const v = (planAdmin || {})[val] || (sponsor || {})[val] || ''
                if(v) str += `; ${v}`
            }
            return str || def;
        }
    },
    'namedFiduciary': {
        label: 'Named Fiduciary for the plan',
        value: (plan:Plan, def = 'See benefits site for details'):Def => {
            const { fiduciary, sponsor } = plan?.info || { fiduciary: { name: undefined }, sponsor: { name: def } }
            let str = fiduciary?.name || '';
            for(const val of ['phone', 'email', 'address']){
                const v = (fiduciary || {})[val] || (sponsor || {})[val] || '';
                if(v) str += `; ${v}`
            }
            return str || def;
        }
    },
    'legalAgent': {
        label: 'Agent for service of legal process',
        value: (plan:Plan, def = 'See benefits site for details'):Def => {
            const { legalAgent, sponsor } = plan?.info || { legalAgent: { name: undefined }, sponsor: { name: def} }
            let str = legalAgent.name || '';
            for(const val of ['phone', 'email', 'address']){
                const v = legalAgent[val] || (sponsor || {})[val] || ''
                if(v) str += `; ${v}`
            }
            return str;
        }
    }
});

export const getCustomValue = (key:string, plan:any, def?:Def):string|number => {
   return customValues.value[key].value(plan, def) || ''
}
export declare type Section = {
    key:string,
    title:string,
    sections: {
        [key:string]:{
            key:string,
            title:string,
            body:string
        }
    }
}
export declare type Sections = {
    [key:string]: Section
}
export declare type Doc = {
    name: string,
    sections: Sections
    [key:string]:any
}

const injectPlanDetails = (str:string, plan:any):string => {
    const regex = /\${([^}]+)}/g;

    // Replace each match with the corresponding value from the valueMap
    return str.replace(regex, (match:any, key:string) => {
        const spl = key.split('||').map((a:string) => a.trim());
        // Check if the key exists in the valueMap
        if (customValues.value.hasOwnProperty(spl[0])) {

            const args = [plan];
            if(spl[1]) args.push(spl[1]);
            // Return the corresponding value
            return customValues.value[spl[0]].value(...args as [any, Def]);
        } else {
            // If the key doesn't exist, return the original match
            return match || '';
        }
    });
}

export const getDisplayPlanDoc = (sections:Sections, plan:any) => {
    for(const sec in sections || {}){
        for(const sub in sections[sec]?.sections || {}){
            const { body } = sections[sec].sections[sub] || { body: '' }
            sections[sec].sections[sub].body = injectPlanDetails(body || '', plan)
        }
    }
    return sections;
}

export const planDocPaths = (plan:Ref<any>|ComputedRef<any>) => {
    const docPaths = computed(() => {
        const p = plan.value || { doc: undefined, spd: undefined };
        const obj:any = {
            'doc': {
                doc: p.doc,
                query: { class: 'core' },
                name: 'Core Plan Doc',
                shortName: 'Core',
                active: true,
            }
        };
        const cafe = p.cafe || {};
        for(const k in cafe){
            const { class:kClass, name, subClass, shortName }:any = cafeKeys[k as keyof typeof cafeKeys] || { shortName: '' }
            obj[`cafe.${k}.doc`] = {
                doc: cafe[k].doc,
                class: kClass,
                active: cafe[k].active,
                subClass,
                name,
                shortName,
                query: { class: kClass, subClass }
            }
        }
        const hra = p.hra || {};
        for(const k in hra){
            const { class:kClass, name, subClass, shortName } = hraKeys[k as keyof typeof hraKeys] || { shortName: '' }
            obj[`hra.${k}.doc`] = {
                doc: hra[k].doc,
                class: kClass,
                active: hra[k].active,
                subClass,
                name,
                shortName,
                query: { class: kClass, subClass }
            }
        }
        return {
            ...obj,
            'spd': {
                doc: p.spd,
                query: { class: 'spd' },
                name: 'Summary Plan Description',
                shortName: 'SPD',
                active: true
            }
        };
    })
    return { docPaths };
}
