export * from './plan-classes';
export * from './rules'

export const getCurrentPlanYear = (plan?: { planYearStart?: string }) => {
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth(); // 0–11

    // Default: just return current year
    if (!plan?.planYearStart) return String(currentYear);

    // Extract the month from the planYearStart (ignores the day/year parts)
    const planStartDate = new Date(plan.planYearStart);
    const planMonth = planStartDate.getMonth(); // 0–11

    // If today is *before* the plan start month, we're still in the plan year that began last year
    // If today is >= plan start month, we're in the plan year that ends next year
    const planYear =
        currentMonth >= (planMonth - 3) ? currentYear + 1 : currentYear;

    return String(planYear);
};
