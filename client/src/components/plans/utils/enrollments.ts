export const getOpenEnrolls = (plan:any) => {
    if(!plan?.enrollments) return [];
    return Object.keys(plan.enrollments).filter(a => plan.enrollments[a].open_enroll).sort((a, b) => b.localeCompare(a))
}
export const getLatestOpen = (plan:any) => {;
    const sorted = getOpenEnrolls(plan);
    return sorted[0]
}

export const currentEnrolled = (plan:any) => {
    const er = plan.enrollments || {};
    const keys = Object.keys(er);
    let total = 0;
    for (let i = 0; i < keys.length; i++) {
        if (er[keys[i]].active) total += er[keys[i]].enrolled || 0
    }
    return total;
}
