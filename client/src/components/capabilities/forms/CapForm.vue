<template>
  <div class="_fw q-pa-sm">

    <template v-if="!confirm">

      <div v-if="title" class="text-weight-bold font-3-4r q-pb-sm">
        {{ title }}
      </div>

      <div class="flex items-center">

        <div>
          scheme:&nbsp;
        </div>
        <q-chip square>
          {{ defaultScheme }}
        </q-chip>
        <div>&nbsp;|&nbsp;</div>
        <div>
          hierPart:&nbsp;
        </div>

        <q-chip
            v-if="form.can.namespace"
            :label="$limitStr(form.with.hierPart, 20, '...')"
            clickable
            icon-right="mdi-menu-down">
        </q-chip>
        <q-chip square v-else>
          {{ form.with.hierPart || defaultHierPart }}
        </q-chip>
      </div>

      <div class="flex items-center" v-if="form.can !== Superuser">
        <div>
          namespace:&nbsp;
        </div>
        <q-chip square icon-right="mdi-menu-down" :label="form.can.namespace">
          <q-menu>
            <q-card class="q-pa-sm __mc">
              <q-input dense v-model="form.can.namespace"></q-input>
              <q-list separator dense>
                <div v-for="(service, i) in extraNamespaces || []" :key="`extra-${i}`" class="_fw">
                  <slot name="extraNamespace" :value="service" :form="form" :setNamespace="setNamespace">
                    <q-item clickable @click="form.can.namespace = service" v-for="(service, i) in namespaces"
                            :key="`service-${i}-${service}`">
                      <q-item-label header>{{ service }}</q-item-label>
                      <q-item-section side>
                        <q-icon v-if="form.can.namespace === service" color="green" name="mdi-check"></q-icon>
                      </q-item-section>
                    </q-item>
                  </slot>
                  <q-separator v-if="i < extraNamespaces?.length - 1"></q-separator>
                </div>
                <q-item clickable @click="form.can.namespace = service"
                        v-for="(service, i) in namespaces"
                        :key="`service-${i}-${service}`">
                  <q-item-label header>{{ service }}</q-item-label>
                  <q-item-section side>
                    <q-icon v-if="form.can.namespace === service" color="green" name="mdi-check"></q-icon>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card>

          </q-menu>
        </q-chip>
        <q-btn dense flat size="xs" color="primary" icon="mdi-plus" v-if="namespace && subScopes[namespace]">
          <q-popup-proxy>
            <div class="w400 mw100 q-pa-sm">
              <q-input dense filled :model-value="searchText" @update:model-value="setSearch"></q-input>
              <q-list separator>
                <q-item v-for="(item, i) in dataManager.data" :key="`item-${i}`" clickable @click="form.can.namespace = `${namespace}:${item._id}`">
                  <q-item-section>
                    <q-item-label>{{item[subScopes[namespace].name]}}</q-item-label>
                    <q-item-label caption>{{item._id}}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </q-popup-proxy>
        </q-btn>
        <div>&nbsp;|&nbsp;</div>
        <div>
          segments:&nbsp;
        </div>
        <q-chip square clickable icon-right="mdi-menu-down" :label="form.can.segments[0] || ''">
          <q-menu>
            <q-card class="q-pa-sm __mc">
              <q-list dense separator>
                <q-item v-for="(action, i) in Object.keys(actions)" :key="`action-${i}`" clickable
                        @click="form.can.segments = [action]">
                  <q-item-label header>{{ actions[action].label }}</q-item-label>
                </q-item>
              </q-list>
            </q-card>
          </q-menu>
        </q-chip>
      </div>

      <div class="flex items-center">
        <q-checkbox
            :model-value="form.can === Superuser"
            size="sm"
            color="purple"
            label="Make superuser for this scheme and hierPart"
            @update:model-value="form.can === Superuser ? form.can = defCan() : form.can = Superuser"
        ></q-checkbox>
      </div>

      <div class="row justify-end q-py-sm">
        <q-btn
            size="sm"
            @click="confirm = true"
            flat
            label="Save"
            icon-right="mdi-content-save"
            color="primary"
        ></q-btn>
      </div>

    </template>

    <q-slide-transition>
      <div class="_fw" v-if="confirm">

        <div>
          ⚠️<span class="font-3-4r text-weight-bold">Wait, let's confirm</span>
          <q-separator class="q-my-xs"></q-separator>

          <div class="flex items-center">
            <div>You want to give&nbsp;</div>
            <default-chip :model-value="_get(subject, subjectPath, subject)"></default-chip>
            <div>the following rights</div>
          </div>
          <cap-item :model-value="form"></cap-item>
        </div>

        <div class="row justify-end">
          <q-btn size="sm" flat icon="mdi-cancel" color="negative" label="No" @click="confirm = false"></q-btn>
          <q-btn size="sm" flat icon="mdi-check" color="positive" label="Yes" @click="save"></q-btn>
        </div>

      </div>
    </q-slide-transition>

  </div>
</template>

<script setup>
  import DefaultChip from 'src/components/common/avatars/DefaultChip.vue';
  import CapItem from 'src/components/capabilities/cards/CapItem.vue';
  import {useServicesExpose} from 'src/stores/services-expose';
  import {HFind} from 'src/utils/hFind';
  import {computed, ref, watch} from 'vue';
  import {_get} from 'symbol-syntax-utils';

  const { api } = useFeathers();

  import {loginPerson} from 'src/stores/utils/login';

  const store = useServicesExpose();
  import {defaultScheme, defaultHierPart} from 'src/utils/ucans';

  const { login } = loginPerson()

  import {SUPERUSER} from '../settings';
  import {$limitStr} from 'src/utils/global-methods';
  import {listCapabilities} from '../utils/list-capabilities';
  import {useFeathers} from 'stores';
  import {HQuery} from 'src/utils/hQuery';

  const Superuser = ref(SUPERUSER);

  const emit = defineEmits(['update:model-value'])
  // import {api} from 'src/api/feathers-client';
  const props = defineProps({
    ucanPath: { type: String, default: 'ucan' },
    subject: { required: true },
    subjectPath: { type: String, default: '_fastjoin.owner' },
    modelValue: [Object],
    title: String,
    extraCaps: Array
  });

  const defWith = computed(() => {
    return { scheme: defaultScheme, hierPart: form.value?.with?.hierPart || defaultHierPart };
  })

  const defCan = () => {
    return {
      namespace: '',
      segments: []
    }
  };

  const form = ref({
    with: {
      scheme: defaultScheme,
      hierPart: defaultHierPart
    },
    can: defCan()
  });

  const subScopes = computed(() => {
    return {
      'orgs': { name: 'name' },
      'plans': { name: 'name' }
    }
  })

  const namespace = computed(() => form.value.can === SUPERUSER ? SUPERUSER : form.value.can.namespace);
  const searchText = ref('');
  const search = computed(() => {
    return {
      text: searchText.value,
      keys: subScopes.value[namespace.value]?.keys || ['name']
    }
  })
  const { searchQ } = HQuery({ search })
  const dataManager = ref({ total: 0, data: [] } );

  const searchTo = ref()
  const setSearch = async (v) => {
    searchText.value = v;
    if(searchTo.value) clearTimeout(searchTo.value);
    searchTo.value = setTimeout(async () => {
      dataManager.value = await api.service(namespace.value).find({ query: {...searchQ.value } })
    }, 1000)
  }

  watch(namespace, async (nv, ov) => {
    if(nv && nv !== ov && subScopes.value[nv]) {
      dataManager.value = await api.service(nv).find({ query: {} })
    }
  }, { immediate: true })

  const { capabilities, ucan } = listCapabilities({ record: login, ucanPath: 'ucan' })
  const setNamespace = (val) => {
    form.value.can.namespace = val;
  }

  const { serverData } = HFind({
    store,
    paginateApi: 'server',
    limit: ref(100),
    params: computed(() => {
      return {
        $limit: 100
      }
    })
  })

  //ensure only relevant capabilities to this user/app combination are displayed
  const filteredCaps = computed(() => (capabilities.value || []).filter(w => JSON.stringify(w.with) === JSON.stringify(defWith.value)));

  const isSuperuser = computed(() => filteredCaps.value.some(a => a.can === SUPERUSER));

  //ensure only namespaces available to the user are selectable
  const extraNamespaces = computed(() => (props.extraCaps || []).map(a => a.can?.namespace || ''));

  const namespaces = computed(() => {
    return (serverData.value?.data || []).map(a => a.service)
        .filter(a => {
          return isSuperuser.value || (filteredCaps.value.map(c => c.can?.namespace).includes(a) && !!a)
        }).sort((a, b) => a.localeCompare(b))
  })

  const save = () => {
    emit('update:model-value', { ...form.value });
    confirm.value = false;
  };

  const confirm = ref(false);

  const actions = computed(() => {
    const obj = {
      'READ': { label: 'Read' },
      'WRITE': { label: 'Write' },
      'CREATE': { label: 'Create' },
      'DELETE': { label: 'Delete' },
      [SUPERUSER]: { label: 'All' }
    }
    const returnObj = {};
    //ensure only actions the user has the ability to offer are selectable
    Object.keys(obj).filter(a => isSuperuser.value || (filteredCaps.value.map(c => _get(c, ['can', 'segments', 0]).includes(a) && !!a))).forEach(key => returnObj[key] = obj[key]);
    return returnObj;
  })

  const mv = computed(() => props.modelValue);

  watch(mv, (nv, ov) => {
    if (nv && JSON.stringify(nv) !== JSON.stringify(ov || '')) {
      form.value = Object.assign({}, nv);
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>
  .__filler {
    padding: 2px 4px;
    background: #eeeeee;
    border-radius: 4px;
  }

  .__mc {
    min-width: 250px
  }
</style>
