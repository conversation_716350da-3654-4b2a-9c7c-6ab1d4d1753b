<template>
  <div class="q-pa-xs _fw mw500">
    <q-input dense filled v-model="search.text">
      <template v-slot:prepend>
        <q-icon name="mdi-magnify"></q-icon>
      </template>
    </q-input>
  </div>
  <div class="__grd">

    <div v-if="!ov" class="q-pa-md font-7-8r">N/A</div>
    <div v-else class="q-pa-sm font-7-8r text-ir-text">
      <div class="flex items-center">
        <div class="text-ir-deep">Amount:&nbsp;</div>
        <div class="tw-six">{{dollarString(ov.amount, '$', 0)}}</div>
      </div>
      <div class="flex items-center">
        <div class="text-ir-deep">Category:&nbsp;</div>
        <div class="tw-six">{{ov.category}}</div>
      </div>
      <div class="flex items-center">
        <div class="text-ir-deep">Name:&nbsp;</div>
        <div class="tw-six">{{ov.name || ''}}</div>
      </div>
      <div class="flex items-center">
        <div class="text-ir-deep">Detail:&nbsp;</div>
        <div class="tw-six">{{ov.detail || ''}}</div>
      </div>
      <div class="flex items-center">
        <div class="text-ir-deep">Remove Categories:&nbsp;</div>
        <cat-chip v-for="(c, i) in oc$.data" :key="`oc-${i}`" :model-value="c"></cat-chip>
      </div>
    </div>


    <div class="q-pa-sm mw100">
      <q-icon size="25px" name="mdi-arrow-right-bold"></q-icon>
    </div>

    <div v-if="!nv" class="q-pa-md font-7-8r">N/A</div>
    <div v-else class="q-pa-sm font-7-8r text-ir-text">
      <div class="flex items-center">
        <div class="text-ir-deep">Amount:&nbsp;</div>
        <div class="tw-six">{{dollarString(nv.amount, '$', 0)}}</div>
      </div>
      <div class="flex items-center">
        <div class="text-ir-deep">Category:&nbsp;</div>
        <div class="tw-six">{{nv.category}}</div>
      </div>
      <div class="flex items-center">
        <div class="text-ir-deep">Name:&nbsp; </div>
        <div class="tw-six">{{nv.name || ''}}</div>
      </div>
      <div class="flex items-center">
        <div class="text-ir-deep">Detail:&nbsp; </div>
        <div class="tw-six">{{nv.detail || ''}}</div>
      </div>
      <div class="flex items-center">
        <div class="text-ir-deep">Add Categories:&nbsp; </div>
        <cat-chip v-for="(c, i) in nc$.data" :key="`nc-${i}`" :model-value="c"></cat-chip>
      </div>
    </div>

  </div>
</template>

<script setup>
  import CatChip from 'components/cats/cards/CatChip.vue';

  import {dollarString} from 'symbol-syntax-utils';
  import {HFind} from 'src/utils/hFind';
  import {computed} from 'vue';
  import {HQuery} from 'src/utils/hQuery';
  import {useCats} from 'stores/cats';

  const catStore = useCats()

  const props = defineProps({
    nv: { required: true },
    ov: { required: true },
  })

  const ocChange = computed(() => {
    if(!props.ov?.cats) return props.nv?.cats || [];
    if(!props.nv?.cats) return props.ov?.cats || [];
    return props.ov.cats.filter(a => !props.nv.cats.includes(a))
  })
  const ncChange = computed(() => {
    if(!props.ov?.cats) return props.nv?.cats || [];
    if(!props.nv?.cats) return props.ov?.cats || [];
    return props.nv.cats.filter(a => !props.ov.cats.includes(a))
  })

  const { search, searchQ } = HQuery({})


  const { h$:oc$ } = HFind({
    store: catStore,
    limit: computed(() => Math.min(25,(ocChange.value || []).length)),
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          _id: { $in: ocChange.value }
        }
      }
    })
  })
  const { h$:nc$ } = HFind({
    store: catStore,
    limit: computed(() => Math.min(25,(ncChange.value || []).length)),
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          _id: { $in: ncChange.value }
        }
      }
    })
  })
</script>

<style lang="scss" scoped>

  .__grd {
    min-height: 50px;
    width: 100%;
    display: grid;
    grid-template-columns: auto minmax(30px, 80px) auto;
  }
</style>
