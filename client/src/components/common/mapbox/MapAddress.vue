<template>
  <div class="map_box">

    <div class="row">
      {{selectedSuggestion}}
    </div>
    <div class="row">
      <div class="col-12 q-pl-xs" v-if="addressPosition === 'top'">
        <map-autocomplete
            item_text="formatted"
            :dark="dark"
            @update:model-value="addressInput"
            @error="searchInput = ''"
            :model-value="selectedSuggestion"
            @clear="newEditedAddress = editedAddress"
        ></map-autocomplete>
      </div>
      <div class="col-12 q-pr-xs">
        <div class="__mb">
          <mapbox
              :geo-in="geoCircle"
              v-if="mapCenter"
              :center="mapCenter"
              fit-bounds
          ></mapbox>
          <q-chip v-if="display" class="__dc" color="white">
            <q-icon color="ir-red-8" name="mdi-map-marker" class="q-mr-xs"></q-icon>
              {{display}}
          </q-chip>
        </div>
      </div>
      <div class="col-12 q-pl-xs" v-if="addressPosition !== 'top'">
        <map-autocomplete
            item_text="formatted"
            :dark="dark"
            @update:model-value="addressInput"
            @error="searchInput = ''"
            :model-value="selectedSuggestion"
            @clear="newEditedAddress = editedAddress"
        ></map-autocomplete>
      </div>
      <div class="col-12">
        <q-slide-transition>
        <div v-if="display" class="q-my-sm font-1r">
          <div class="flex items-center">
            <div>Within</div>
            <q-chip
                class="text-weight-bold"
                clickable
                color="white"
                :label="`${kmToMi(bKm)} mi`"
                icon-right="mdi-menu-down">
              <q-menu>
                <q-list dense separator>
                  <q-item v-for="i in miles" :key="`mile-${i}`" clickable @click="bKm = miToKm(i)">
                    <q-item-label>{{i}}</q-item-label>
                  </q-item>
                </q-list>
              </q-menu>
            </q-chip>
          </div>
          <div class="text-weight-bold">{{display}}</div>
        </div>
        </q-slide-transition>
      </div>
    </div>

  </div>
</template>

<script setup>
  import {computed, onMounted, ref, watch, nextTick} from 'vue';
  import MapAutocomplete from '../address/tomtom/TomtomAutocomplete.vue';
  import Mapbox from './map/MapBox.vue';
  import {SessionStorage} from 'symbol-auth-client';
  import {_get} from 'symbol-syntax-utils';
  import { pointToGeo, isCoordinate, createGeoJSONCircle, kmToMi, miToKm } from 'src/utils/geo-utils'

  const props = defineProps({
    addressPosition: { type: String, default: 'top' },
    dark: Boolean,
    km: { type: Number, default: miToKm(40) },
    modelValue: { required: true },
    display: String,
    editedAddress: {
      type: Object,
      required: false,
      default: function () {
        return {
          name: '',
          formatted: '',
          address1: '',
          address2: '',
          region: '',
          route: '',
          city: '',
          postal: '',
          country: '',
          latitude: 0,
          longitude: 0,
        };
      }
    },
  });
  const emit = defineEmits(['update:model-value']);

  const ll = ref([-80.847441, 35.191346]);
  const selectedSuggestion = ref('');
  const searchInput = ref('');
  const newEditedAddress = ref({ ...props.editedAddress })

  const bKm = ref(props.km || miToKm(40));
  const ea = computed(() => {
    return props.editedAddress;
  });
  watch(ea, nv => {
    if (nv) newEditedAddress.value = nv;
  });
  const featureList = computed(() => {
    let arr = props.modelValue;
    if (!Array.isArray(props.modelValue)) arr = [props.modelValue];
    return isCoordinate(arr) ? [pointToGeo(arr)] : arr.map(a => pointToGeo(a)).filter(a => !!a);
  });
  const mapCenter = computed(() => {
    const f0 = (featureList.value || [undefined])[0];
    const f = f0?.geometry;
    const addressGeo = _get(f0, ['addresses', 0, 'lngLat']);
    if (!f0 || (!f && !addressGeo)) return ll.value;
    else {
      let geo = f?.type === 'Point' ? f?.coordinates : f.coordinates[0];
      if(!geo) geo = addressGeo;
      return geo;
    }
  });

  const geoCircle = computed(() =>{
    return mapCenter.value && mapCenter.value[1] ? createGeoJSONCircle(mapCenter.value, bKm.value) : undefined;
  });

  const ms = () => {
    let list = [1,2,3,4,5,10,15,20,25];
    for(let i = 11; i < 70; i++){
      if(i < 29) list.push((i - 10) * 10 + 20);
      else list.push((i - 29) * 50 + 250)
    }
    return list;
  };
  const miles = ref(ms());

  const features = computed(() => {
    return featureList.value.map(a => a.geometry);
  });
  const addressInput = (val) => {
    newEditedAddress.value = { ...val };
    selectedSuggestion.value = { ...val };
    saveAddress();
  };
  const saveAddress = () => {
    console.log('save address', selectedSuggestion.value);
    if (!selectedSuggestion.value) {
      let c = confirm(`Map Services doesn't recognize this address. Use "${searchInput.value}" anyway?`);
      if (c) {
        selectedSuggestion.value = { name: '', description: searchInput.value };
        newEditedAddress.value = { name: '', formatted: searchInput.value };
        const payload = newEditedAddress.value;
        nextTick(() => emit('update:model-value', payload));
        nextTick(() => {
          selectedSuggestion.value = '';
          searchInput.value = '';
        });
      }
    } else {
      const payload = newEditedAddress.value;
      emit('update:model-value', payload);
      selectedSuggestion.value = '';
      searchInput.value = '';
    }
  };
  onMounted(() => {
    const print = SessionStorage.getItem('sessionPrint');
    console.log('print?', print);
    if (print?.ipInfo?.lngLat) ll.value = print.ipInfo.lngLat;
  });
</script>

<style scoped>
  .map_box {
    position: relative;
    height: 100%;
    width: 100%;
  }
  .__mb {
    position: relative;
    height: 100%;
    width: 100%;
    min-height: 400px;
  }
  .__dc {
    position: absolute;
    z-index: 2;
    top: 3px;
    left: 3px;
  }
</style>
