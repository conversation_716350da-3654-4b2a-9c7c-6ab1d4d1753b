// src/stores/auth.ts
import { defineStore } from 'pinia';
import { useAuth as FeathersUseAuth } from 'feathers-pinia';
import { useFeathers } from './index';
import { useRouter } from 'vue-router';
import {LocalStorage, SessionStorage} from 'symbol-auth-client';

export const useAuth = defineStore('auth', () => {
	const { api } = useFeathers();
	const router = useRouter();
	const onSuccess = (val:any) => {
		if(val.login){
			LocalStorage.setItem('ucan_aud', val.login?.did);
			LocalStorage.setItem('client_ucan', val.login?.ucan);
			SessionStorage.setItem('client_ucan', val.login?.ucan);
		} else {
			LocalStorage.setItem('client_ucan', val.accessToken);
			SessionStorage.setItem('client_ucan', val.accessToken);
		}
		return val;
	};

	const utils = FeathersUseAuth({
		api,
		servicePath: 'logins',
		entityKey: 'login',
		onSuccess
		// onSuccess: async (result) => {
		// 	console.log('auth success', result);
		// },
		// onInitSuccess: async (val) => {
		// 	console.log('init successs', val);
		// 	// loginStore['store'].createInStore(val);
		// 	// await loginStore.setLoginId(val.login._id);
		// }
	});

	utils.reAuthenticate();

	return {
		...utils,
		logout: async () => {
			// Clear auth tokens before logout
			LocalStorage.removeItem('ucan_aud');
			LocalStorage.removeItem('client_ucan');
			SessionStorage.removeItem('client_ucan');
			SessionStorage.removeItem('ucan_aud');
			SessionStorage.removeItem('cap_ucans');
			await utils.logout();
			router.go(0);
		}
	};
});

